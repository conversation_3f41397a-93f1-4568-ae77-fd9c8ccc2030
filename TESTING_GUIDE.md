# 📱 Flori Construction Admin - Testing Guide

## 🎯 Current Features to Test

### ✅ **Core App Functionality**
1. **App Launch**: App should start with splash screen and load login page
2. **Navigation**: Sidebar menu should work smoothly
3. **Responsive Design**: App should adapt to different screen orientations

### ✅ **Native Features**
1. **Camera Access**: 
   - Tap "Camera" button in sidebar
   - Should open device camera
   - Take a photo and verify it's captured
   
2. **Gallery Access**:
   - Tap "Gallery" button in sidebar
   - Should open photo gallery
   - Select an image and verify it's loaded

3. **Device Information**:
   - Tap "Device Info" button
   - Should show device details in a popup

4. **Network Status**:
   - Check network indicator in header
   - Toggle WiFi/mobile data to test offline/online detection

### ✅ **Storage & Offline**
1. **SQLite Database**: Data should persist between app sessions
2. **File System**: App should have access to device storage
3. **Offline Mode**: App should work without internet connection

## 🧪 Testing Steps

### **1. Basic Functionality Test**
```
1. Launch the app
2. Verify splash screen appears
3. Check login screen loads
4. Test sidebar navigation
5. Verify all menu items are clickable
```

### **2. Camera Feature Test**
```
1. Open sidebar
2. Tap "Camera" button
3. Grant camera permission if prompted
4. Take a photo
5. Verify photo is captured successfully
6. Check if photo appears in app (if implemented)
```

### **3. Gallery Feature Test**
```
1. Open sidebar
2. Tap "Gallery" button
3. Grant storage permission if prompted
4. Select an existing photo
5. Verify photo is loaded successfully
```

### **4. Device Info Test**
```
1. Open sidebar
2. Tap "Device Info" button
3. Verify popup shows:
   - Device manufacturer and model
   - Android version
   - Network status
```

### **5. Network Status Test**
```
1. Check network icon in header (should be green/connected)
2. Turn off WiFi and mobile data
3. Verify icon changes to offline state
4. Turn network back on
5. Verify icon returns to online state
```

## 🐛 Common Issues & Solutions

### **Camera Not Working**
- **Issue**: Camera button doesn't respond
- **Solution**: Check if camera permissions are granted in Android settings

### **Gallery Access Denied**
- **Issue**: Gallery button shows permission error
- **Solution**: Grant storage permissions in Android settings

### **App Crashes on Launch**
- **Issue**: App closes immediately after opening
- **Solution**: Check device logs with `adb logcat`

### **Network Status Not Updating**
- **Issue**: Network indicator doesn't change
- **Solution**: This is normal - network detection may take a few seconds

## 📊 Performance Testing

### **Memory Usage**
- App should use reasonable memory (< 100MB)
- No memory leaks during normal usage

### **Battery Usage**
- App should not drain battery excessively
- Background usage should be minimal

### **Storage Usage**
- App size should be reasonable (< 20MB)
- Data storage should be efficient

## 🔧 Debug Information

### **View Logs**
```bash
# Connect device and run:
adb logcat | grep "CordovaActivity"
```

### **Chrome DevTools**
```
1. Open Chrome browser
2. Go to chrome://inspect
3. Find your device and app
4. Click "Inspect" to debug
```

### **App Information**
- **Package Name**: com.floriconstructionltd.admin
- **Version**: 1.0.0
- **Min Android**: 7.0 (API 24)
- **Target Android**: 13 (API 34)

## 📝 Test Results Template

```
Date: ___________
Device: ___________
Android Version: ___________

✅ App Launch: Pass/Fail
✅ Camera Access: Pass/Fail
✅ Gallery Access: Pass/Fail
✅ Device Info: Pass/Fail
✅ Network Status: Pass/Fail
✅ Navigation: Pass/Fail
✅ Performance: Pass/Fail

Notes:
_________________________________
_________________________________
_________________________________
```

## 🚀 Next Steps After Testing

### **If All Tests Pass:**
1. Create production build: `./build.sh release`
2. Sign APK for distribution
3. Upload to Google Play Store or distribute internally

### **If Issues Found:**
1. Document specific issues
2. Check logs for error details
3. Report issues for fixing

### **Additional Features to Add:**
1. Push notifications (requires Firebase setup)
2. File upload to server
3. Enhanced offline synchronization
4. Biometric authentication

## 📞 Support

If you encounter any issues during testing:
1. Check this guide first
2. Review the logs using `adb logcat`
3. Test on different devices if possible
4. Document the exact steps that cause issues

---

**Happy Testing!** 🎉

Your Flori Construction Admin app is now ready for comprehensive testing with native Android features!
