<?php
/**
 * Projects API for Flori Construction Ltd Mobile App
 * Handles CRUD operations for projects
 */

require_once dirname(__DIR__) . '/config/config.php';
require_once __DIR__ . '/auth-helper.php';
require_once __DIR__ . '/auth.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    switch ($method) {
        case 'GET':
            handleGetProjects();
            break;
            
        case 'POST':
            $user = authenticateRequest();
            if (!$user) {
                jsonResponse(['error' => 'Authentication required'], 401);
            }
            handleCreateProject($input, $user);
            break;
            
        case 'PUT':
            $user = authenticateRequest();
            if (!$user) {
                jsonResponse(['error' => 'Authentication required'], 401);
            }
            handleUpdateProject($input, $user);
            break;
            
        case 'DELETE':
            $user = authenticateRequest();
            if (!$user) {
                jsonResponse(['error' => 'Authentication required'], 401);
            }
            handleDeleteProject($user);
            break;
            
        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }
} catch (Exception $e) {
    jsonResponse(['error' => $e->getMessage()], 500);
}

function handleGetProjects() {
    global $db;
    
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : ITEMS_PER_PAGE;
    $type = isset($_GET['type']) ? sanitize($_GET['type']) : '';
    $featured = isset($_GET['featured']) ? (bool)$_GET['featured'] : false;
    $search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
    
    $offset = ($page - 1) * $limit;
    
    // Build query
    $where = ['is_active = 1'];
    $params = [];
    
    if ($type && in_array($type, ['completed', 'ongoing'])) {
        $where[] = 'project_type = ?';
        $params[] = $type;
    }
    
    if ($featured) {
        $where[] = 'is_featured = 1';
    }
    
    if ($search) {
        $where[] = '(title LIKE ? OR description LIKE ? OR location LIKE ?)';
        $searchTerm = "%{$search}%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $whereClause = implode(' AND ', $where);
    
    // Get total count
    $totalQuery = "SELECT COUNT(*) as total FROM projects WHERE {$whereClause}";
    $totalResult = $db->fetchOne($totalQuery, $params);
    $total = $totalResult['total'];
    
    // Get projects
    $query = "SELECT * FROM projects WHERE {$whereClause} ORDER BY sort_order ASC, created_at DESC LIMIT {$limit} OFFSET {$offset}";
    $projects = $db->fetchAll($query, $params);
    
    // Process gallery JSON
    foreach ($projects as &$project) {
        $project['gallery'] = $project['gallery'] ? json_decode($project['gallery'], true) : [];
        $project['services'] = $project['services'] ? json_decode($project['services'], true) : [];
        
        // Add full URLs for images
        if ($project['featured_image']) {
            $project['featured_image_url'] = UPLOAD_URL . '/' . $project['featured_image'];
        }
        
        if (!empty($project['gallery'])) {
            foreach ($project['gallery'] as &$image) {
                $image['url'] = UPLOAD_URL . '/' . $image['path'];
            }
        }
    }
    
    jsonResponse([
        'success' => true,
        'projects' => $projects,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'total' => $total,
            'pages' => ceil($total / $limit)
        ]
    ]);
}

function handleCreateProject($input, $user) {
    global $db;
    
    // Validate required fields
    $required = ['title', 'project_type'];
    foreach ($required as $field) {
        if (!isset($input[$field]) || empty($input[$field])) {
            jsonResponse(['error' => "Field '{$field}' is required"], 400);
        }
    }
    
    // Validate project type
    if (!in_array($input['project_type'], ['completed', 'ongoing'])) {
        jsonResponse(['error' => 'Invalid project type'], 400);
    }
    
    // Generate slug
    $slug = generateSlug($input['title']);
    
    // Check if slug exists
    $existingProject = $db->fetchOne("SELECT id FROM projects WHERE slug = ?", [$slug]);
    if ($existingProject) {
        $slug .= '-' . time();
    }
    
    // Prepare data
    $data = [
        'title' => sanitize($input['title']),
        'slug' => $slug,
        'description' => isset($input['description']) ? sanitize($input['description']) : '',
        'short_description' => isset($input['short_description']) ? sanitize($input['short_description']) : '',
        'client_name' => isset($input['client_name']) ? sanitize($input['client_name']) : '',
        'location' => isset($input['location']) ? sanitize($input['location']) : '',
        'project_type' => $input['project_type'],
        'start_date' => isset($input['start_date']) ? $input['start_date'] : null,
        'end_date' => isset($input['end_date']) ? $input['end_date'] : null,
        'project_value' => isset($input['project_value']) ? (float)$input['project_value'] : null,
        'is_featured' => isset($input['is_featured']) ? (bool)$input['is_featured'] : false,
        'sort_order' => isset($input['sort_order']) ? (int)$input['sort_order'] : 0,
        'meta_title' => isset($input['meta_title']) ? sanitize($input['meta_title']) : '',
        'meta_description' => isset($input['meta_description']) ? sanitize($input['meta_description']) : '',
        'services' => isset($input['services']) ? json_encode($input['services']) : null,
        'gallery' => isset($input['gallery']) ? json_encode($input['gallery']) : null
    ];
    
    $projectId = $db->insert('projects', $data);
    
    // Get the created project
    $project = $db->fetchOne("SELECT * FROM projects WHERE id = ?", [$projectId]);
    $project['gallery'] = $project['gallery'] ? json_decode($project['gallery'], true) : [];
    $project['services'] = $project['services'] ? json_decode($project['services'], true) : [];
    
    jsonResponse([
        'success' => true,
        'message' => 'Project created successfully',
        'project' => $project
    ]);
}

function handleUpdateProject($input, $user) {
    global $db;
    
    if (!isset($_GET['id'])) {
        jsonResponse(['error' => 'Project ID required'], 400);
    }
    
    $projectId = (int)$_GET['id'];
    
    // Check if project exists
    $existingProject = $db->fetchOne("SELECT * FROM projects WHERE id = ? AND is_active = 1", [$projectId]);
    if (!$existingProject) {
        jsonResponse(['error' => 'Project not found'], 404);
    }
    
    // Prepare update data
    $data = [];
    $allowedFields = [
        'title', 'description', 'short_description', 'client_name', 'location',
        'project_type', 'start_date', 'end_date', 'project_value', 'is_featured',
        'sort_order', 'meta_title', 'meta_description'
    ];
    
    foreach ($allowedFields as $field) {
        if (isset($input[$field])) {
            if (in_array($field, ['title', 'description', 'short_description', 'client_name', 'location', 'meta_title', 'meta_description'])) {
                $data[$field] = sanitize($input[$field]);
            } else {
                $data[$field] = $input[$field];
            }
        }
    }
    
    // Update slug if title changed
    if (isset($input['title']) && $input['title'] !== $existingProject['title']) {
        $newSlug = generateSlug($input['title']);
        $slugExists = $db->fetchOne("SELECT id FROM projects WHERE slug = ? AND id != ?", [$newSlug, $projectId]);
        if ($slugExists) {
            $newSlug .= '-' . time();
        }
        $data['slug'] = $newSlug;
    }
    
    // Handle JSON fields
    if (isset($input['services'])) {
        $data['services'] = json_encode($input['services']);
    }
    
    if (isset($input['gallery'])) {
        $data['gallery'] = json_encode($input['gallery']);
    }
    
    // Validate project type if provided
    if (isset($data['project_type']) && !in_array($data['project_type'], ['completed', 'ongoing'])) {
        jsonResponse(['error' => 'Invalid project type'], 400);
    }
    
    if (empty($data)) {
        jsonResponse(['error' => 'No valid fields to update'], 400);
    }
    
    $data['updated_at'] = date('Y-m-d H:i:s');
    
    $db->update('projects', $data, 'id = ?', [$projectId]);
    
    // Get updated project
    $project = $db->fetchOne("SELECT * FROM projects WHERE id = ?", [$projectId]);
    $project['gallery'] = $project['gallery'] ? json_decode($project['gallery'], true) : [];
    $project['services'] = $project['services'] ? json_decode($project['services'], true) : [];
    
    jsonResponse([
        'success' => true,
        'message' => 'Project updated successfully',
        'project' => $project
    ]);
}

function handleDeleteProject($user) {
    global $db;
    
    if (!isset($_GET['id'])) {
        jsonResponse(['error' => 'Project ID required'], 400);
    }
    
    $projectId = (int)$_GET['id'];
    
    // Check if project exists
    $project = $db->fetchOne("SELECT * FROM projects WHERE id = ? AND is_active = 1", [$projectId]);
    if (!$project) {
        jsonResponse(['error' => 'Project not found'], 404);
    }
    
    // Soft delete (set is_active to 0)
    $db->update('projects', 
        ['is_active' => 0, 'updated_at' => date('Y-m-d H:i:s')], 
        'id = ?', 
        [$projectId]
    );
    
    jsonResponse([
        'success' => true,
        'message' => 'Project deleted successfully'
    ]);
}
?>
