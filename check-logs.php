<?php
/**
 * Check PHP Error Logs
 * This script helps view recent PHP error logs to debug API issues
 */

echo "<h1>📋 PHP Error Log Viewer</h1>";

echo "<style>
    body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; }
    .log-section { background: #f8f9fa; padding: 15px; margin: 15px 0; border-radius: 5px; }
    .error-log { background: #f8d7da; border-left: 4px solid #dc3545; }
    .mobile-log { background: #d4edda; border-left: 4px solid #28a745; }
    .warning-log { background: #fff3cd; border-left: 4px solid #ffc107; }
    pre { background: #f1f1f1; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
    .log-entry { margin: 5px 0; padding: 8px; background: white; border-radius: 3px; }
    .timestamp { color: #666; font-size: 11px; }
    .clear-logs { background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
    .refresh { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px; }
</style>";

// Function to get PHP error log path
function getErrorLogPath() {
    $paths = [
        ini_get('error_log'),
        '/xampp/apache/logs/error.log',
        '/xampp/php/logs/php_error_log',
        'C:/xampp/apache/logs/error.log',
        'C:/xampp/php/logs/php_error_log',
        '/var/log/apache2/error.log',
        '/var/log/php_errors.log'
    ];
    
    foreach ($paths as $path) {
        if ($path && file_exists($path)) {
            return $path;
        }
    }
    
    return false;
}

// Function to read recent log entries
function getRecentLogs($logPath, $lines = 100) {
    if (!file_exists($logPath)) {
        return false;
    }
    
    $file = file($logPath);
    if (!$file) {
        return false;
    }
    
    return array_slice($file, -$lines);
}

// Function to filter mobile API logs
function filterMobileLogs($logs) {
    return array_filter($logs, function($log) {
        return strpos($log, 'Mobile API:') !== false || 
               strpos($log, 'mobile.php') !== false ||
               strpos($log, 'Project error') !== false;
    });
}

// Handle log clearing
if (isset($_POST['clear_logs'])) {
    $logPath = getErrorLogPath();
    if ($logPath && file_exists($logPath)) {
        file_put_contents($logPath, '');
        echo "<div class='log-section mobile-log'>";
        echo "<p>✅ Error log cleared successfully</p>";
        echo "</div>";
    }
}

echo "<div class='log-section'>";
echo "<h2>🔧 Log Management</h2>";
echo "<form method='post' style='display: inline;'>";
echo "<button type='submit' class='refresh' onclick='window.location.reload(); return false;'>🔄 Refresh Logs</button>";
echo "<button type='submit' name='clear_logs' class='clear-logs' onclick='return confirm(\"Are you sure you want to clear the error log?\")'>🗑️ Clear Logs</button>";
echo "</form>";
echo "</div>";

// Get error log path
$logPath = getErrorLogPath();

if (!$logPath) {
    echo "<div class='log-section error-log'>";
    echo "<h2>❌ Error Log Not Found</h2>";
    echo "<p>Could not locate PHP error log. Checked paths:</p>";
    echo "<ul>";
    echo "<li>" . ini_get('error_log') . "</li>";
    echo "<li>/xampp/apache/logs/error.log</li>";
    echo "<li>C:/xampp/apache/logs/error.log</li>";
    echo "<li>/var/log/apache2/error.log</li>";
    echo "</ul>";
    echo "<p>Current error_log setting: " . ini_get('error_log') . "</p>";
    echo "</div>";
} else {
    echo "<div class='log-section'>";
    echo "<h2>📁 Error Log Location</h2>";
    echo "<p><strong>Path:</strong> {$logPath}</p>";
    echo "<p><strong>Size:</strong> " . number_format(filesize($logPath)) . " bytes</p>";
    echo "<p><strong>Last Modified:</strong> " . date('Y-m-d H:i:s', filemtime($logPath)) . "</p>";
    echo "</div>";
    
    // Get recent logs
    $recentLogs = getRecentLogs($logPath, 200);
    
    if ($recentLogs) {
        // Filter for mobile API logs
        $mobileLogs = filterMobileLogs($recentLogs);
        
        if (!empty($mobileLogs)) {
            echo "<div class='log-section mobile-log'>";
            echo "<h2>📱 Mobile API Logs (Recent)</h2>";
            echo "<div style='max-height: 400px; overflow-y: auto;'>";
            foreach (array_reverse($mobileLogs) as $log) {
                echo "<div class='log-entry'>";
                echo "<div class='timestamp'>" . htmlspecialchars(substr($log, 0, 25)) . "</div>";
                echo "<div>" . htmlspecialchars(substr($log, 25)) . "</div>";
                echo "</div>";
            }
            echo "</div>";
            echo "</div>";
        }
        
        // Show all recent logs
        echo "<div class='log-section'>";
        echo "<h2>📋 All Recent Logs (Last 50 entries)</h2>";
        echo "<div style='max-height: 500px; overflow-y: auto;'>";
        $recentEntries = array_slice(array_reverse($recentLogs), 0, 50);
        foreach ($recentEntries as $log) {
            $logClass = '';
            if (strpos($log, 'Mobile API:') !== false) {
                $logClass = 'mobile-log';
            } elseif (strpos($log, 'error') !== false || strpos($log, 'Error') !== false) {
                $logClass = 'error-log';
            } elseif (strpos($log, 'warning') !== false || strpos($log, 'Warning') !== false) {
                $logClass = 'warning-log';
            }
            
            echo "<div class='log-entry {$logClass}'>";
            echo "<div class='timestamp'>" . htmlspecialchars(substr($log, 0, 25)) . "</div>";
            echo "<div>" . htmlspecialchars(substr($log, 25)) . "</div>";
            echo "</div>";
        }
        echo "</div>";
        echo "</div>";
    } else {
        echo "<div class='log-section warning-log'>";
        echo "<h2>⚠️ No Recent Logs</h2>";
        echo "<p>No recent log entries found or unable to read log file.</p>";
        echo "</div>";
    }
}

// Show current PHP configuration
echo "<div class='log-section'>";
echo "<h2>⚙️ PHP Configuration</h2>";
echo "<ul>";
echo "<li><strong>Error Reporting:</strong> " . error_reporting() . "</li>";
echo "<li><strong>Display Errors:</strong> " . ini_get('display_errors') . "</li>";
echo "<li><strong>Log Errors:</strong> " . ini_get('log_errors') . "</li>";
echo "<li><strong>Error Log:</strong> " . ini_get('error_log') . "</li>";
echo "<li><strong>PHP Version:</strong> " . PHP_VERSION . "</li>";
echo "</ul>";
echo "</div>";

// Test project API directly
echo "<div class='log-section'>";
echo "<h2>🧪 Quick API Test</h2>";
echo "<p>Test the project API endpoint directly:</p>";

try {
    require_once 'config/config.php';
    
    // Test database connection
    $testResult = $db->fetchOne("SELECT COUNT(*) as count FROM projects WHERE is_active = 1");
    echo "<p>✅ Database connection: OK ({$testResult['count']} active projects)</p>";
    
    // Test specific project
    $testProject = $db->fetchOne("SELECT id, title, gallery FROM projects WHERE id = 7 AND is_active = 1");
    if ($testProject) {
        echo "<p>✅ Project 7 found: " . htmlspecialchars($testProject['title']) . "</p>";
        echo "<p>Gallery data: " . htmlspecialchars($testProject['gallery'] ?? 'null') . "</p>";
    } else {
        echo "<p>❌ Project 7 not found</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Database test failed: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

echo "<div class='log-section'>";
echo "<h2>🔗 Useful Links</h2>";
echo "<ul>";
echo "<li><a href='test-project-api.php' target='_blank'>Project API Test</a></li>";
echo "<li><a href='mobile-app/debug-auth.html' target='_blank'>Authentication Debug Tool</a></li>";
echo "<li><a href='mobile-app/' target='_blank'>Mobile App</a></li>";
echo "</ul>";
echo "</div>";

echo "<script>
// Auto-refresh every 30 seconds if there are mobile logs
if (document.querySelector('.mobile-log')) {
    setTimeout(() => {
        window.location.reload();
    }, 30000);
}
</script>";
?>
