<?php
/**
 * Content Management API
 * Handles content CRUD operations for the mobile app
 */

require_once dirname(__DIR__) . '/config/config.php';
require_once __DIR__ . '/auth-helper.php';
require_once dirname(__DIR__) . '/config/email.php';

// Set JSON header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    switch ($method) {
        case 'GET':
            handleGetContent();
            break;
            
        case 'PUT':
            $user = authenticateRequest();
            if (!$user) {
                jsonResponse(['error' => 'Authentication required'], 401);
            }
            handleUpdateContent($input, $user);
            break;
            
        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }
} catch (Exception $e) {
    jsonResponse(['error' => $e->getMessage()], 500);
}

function handleGetContent() {
    global $db;
    
    try {
        // Get all content sections
        $content = $db->fetchAll("SELECT * FROM content WHERE is_active = 1 ORDER BY section_key");
        
        // Get site content
        $siteContent = $db->fetchAll("SELECT * FROM site_content ORDER BY content_key");
        
        // Get site settings
        $siteSettings = $db->fetchAll("SELECT * FROM site_settings ORDER BY setting_key");
        
        // Format response
        $response = [
            'success' => true,
            'content' => [],
            'site_content' => [],
            'site_settings' => []
        ];
        
        // Format content
        foreach ($content as $item) {
            $response['content'][$item['section_key']] = [
                'id' => $item['id'],
                'title' => $item['title'],
                'content' => $item['content'],
                'meta_data' => $item['meta_data'] ? json_decode($item['meta_data'], true) : null,
                'updated_at' => $item['updated_at']
            ];
        }
        
        // Format site content
        foreach ($siteContent as $item) {
            $response['site_content'][$item['content_key']] = [
                'id' => $item['id'],
                'value' => $item['content_value'],
                'updated_at' => $item['updated_at']
            ];
        }
        
        // Format site settings
        foreach ($siteSettings as $item) {
            $response['site_settings'][$item['setting_key']] = [
                'id' => $item['id'],
                'value' => $item['setting_value'],
                'updated_at' => $item['updated_at']
            ];
        }
        
        jsonResponse($response);
        
    } catch (Exception $e) {
        jsonResponse(['error' => 'Failed to fetch content: ' . $e->getMessage()], 500);
    }
}

function handleUpdateContent($input, $user) {
    global $db;
    
    if (!isset($input['type']) || !isset($input['key']) || !isset($input['value'])) {
        jsonResponse(['error' => 'Missing required fields'], 400);
    }
    
    $type = $input['type']; // 'content', 'site_content', or 'site_settings'
    $key = sanitize($input['key']);
    $value = $input['value'];
    
    try {
        switch ($type) {
            case 'content':
                // Update main content table
                $title = sanitize($input['title'] ?? '');
                $content = $input['value'];
                $metaData = isset($input['meta_data']) ? json_encode($input['meta_data']) : null;
                
                $existing = $db->fetchOne("SELECT id FROM content WHERE section_key = ?", [$key]);
                
                if ($existing) {
                    $db->update('content', [
                        'title' => $title,
                        'content' => $content,
                        'meta_data' => $metaData,
                        'updated_by' => $user['id'],
                        'updated_at' => date('Y-m-d H:i:s')
                    ], 'section_key = ?', [$key]);
                } else {
                    $db->insert('content', [
                        'section_key' => $key,
                        'title' => $title,
                        'content' => $content,
                        'meta_data' => $metaData,
                        'updated_by' => $user['id'],
                        'is_active' => 1,
                        'created_at' => date('Y-m-d H:i:s')
                    ]);
                }
                break;
                
            case 'site_content':
                // Update site content table
                $existing = $db->fetchOne("SELECT id FROM site_content WHERE content_key = ?", [$key]);
                
                if ($existing) {
                    $db->update('site_content', [
                        'content_value' => $value,
                        'updated_at' => date('Y-m-d H:i:s')
                    ], 'content_key = ?', [$key]);
                } else {
                    $db->insert('site_content', [
                        'content_key' => $key,
                        'content_value' => $value,
                        'created_at' => date('Y-m-d H:i:s')
                    ]);
                }
                break;
                
            case 'site_settings':
                // Update site settings table
                $existing = $db->fetchOne("SELECT id FROM site_settings WHERE setting_key = ?", [$key]);
                
                if ($existing) {
                    $db->update('site_settings', [
                        'setting_value' => is_array($value) ? json_encode($value) : $value,
                        'updated_at' => date('Y-m-d H:i:s')
                    ], 'setting_key = ?', [$key]);
                } else {
                    $db->insert('site_settings', [
                        'setting_key' => $key,
                        'setting_value' => is_array($value) ? json_encode($value) : $value,
                        'created_at' => date('Y-m-d H:i:s')
                    ]);
                }
                break;
                
            default:
                jsonResponse(['error' => 'Invalid content type'], 400);
                return;
        }
        
        jsonResponse([
            'success' => true,
            'message' => 'Content updated successfully',
            'updated_by' => $user['full_name'],
            'updated_at' => date('Y-m-d H:i:s')
        ]);
        
    } catch (Exception $e) {
        jsonResponse(['error' => 'Failed to update content: ' . $e->getMessage()], 500);
    }
}

?>
