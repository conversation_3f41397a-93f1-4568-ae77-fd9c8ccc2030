<?php
/**
 * Debug Authentication Issues
 * Simple test script to identify PHP errors
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Authentication Debug</h1>\n";
echo "<p>Testing authentication components...</p>\n";

// Test 1: Check if config loads
echo "<h2>1. Testing Config Load</h2>\n";
try {
    require_once dirname(__DIR__) . '/config/config.php';
    echo "<p>✅ Config loaded successfully</p>\n";
    
    // Test database connection
    if (isset($db)) {
        echo "<p>✅ Database object exists</p>\n";
        
        // Test a simple query
        $result = $db->fetchOne("SELECT 1 as test");
        if ($result) {
            echo "<p>✅ Database connection working</p>\n";
        } else {
            echo "<p>❌ Database query failed</p>\n";
        }
    } else {
        echo "<p>❌ Database object not found</p>\n";
    }
} catch (Exception $e) {
    echo "<p>❌ Config error: " . $e->getMessage() . "</p>\n";
}

// Test 2: Check if auth-helper loads
echo "<h2>2. Testing Auth Helper Load</h2>\n";
try {
    require_once __DIR__ . '/auth-helper.php';
    echo "<p>✅ Auth helper loaded successfully</p>\n";
    
    // Test if functions exist
    if (function_exists('authenticateRequest')) {
        echo "<p>✅ authenticateRequest function exists</p>\n";
    } else {
        echo "<p>❌ authenticateRequest function missing</p>\n";
    }
    
    if (function_exists('getBearerToken')) {
        echo "<p>✅ getBearerToken function exists</p>\n";
    } else {
        echo "<p>❌ getBearerToken function missing</p>\n";
    }
    
    if (function_exists('generateToken')) {
        echo "<p>✅ generateToken function exists</p>\n";
    } else {
        echo "<p>❌ generateToken function missing</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Auth helper error: " . $e->getMessage() . "</p>\n";
}

// Test 3: Check if auth.php loads without errors
echo "<h2>3. Testing Auth.php Load</h2>\n";
try {
    // Capture any output
    ob_start();
    
    // Temporarily disable the direct execution check
    $_SERVER['SCRIPT_NAME'] = 'debug-auth.php';
    
    include __DIR__ . '/auth.php';
    
    $output = ob_get_clean();
    
    if (empty($output)) {
        echo "<p>✅ Auth.php loaded without output</p>\n";
    } else {
        echo "<p>⚠️ Auth.php produced output:</p>\n";
        echo "<pre>" . htmlspecialchars($output) . "</pre>\n";
    }
    
} catch (Exception $e) {
    ob_end_clean();
    echo "<p>❌ Auth.php error: " . $e->getMessage() . "</p>\n";
}

// Test 4: Test user lookup
echo "<h2>4. Testing User Lookup</h2>\n";
try {
    if (isset($db)) {
        $user = $db->fetchOne(
            "SELECT id, username, email, full_name, role, is_active FROM users WHERE username = ? AND is_active = 1",
            ['admin']
        );
        
        if ($user) {
            echo "<p>✅ Admin user found:</p>\n";
            echo "<ul>\n";
            echo "<li>ID: " . $user['id'] . "</li>\n";
            echo "<li>Username: " . $user['username'] . "</li>\n";
            echo "<li>Email: " . $user['email'] . "</li>\n";
            echo "<li>Role: " . $user['role'] . "</li>\n";
            echo "</ul>\n";
        } else {
            echo "<p>❌ Admin user not found</p>\n";
        }
    }
} catch (Exception $e) {
    echo "<p>❌ User lookup error: " . $e->getMessage() . "</p>\n";
}

// Test 5: Test password verification
echo "<h2>5. Testing Password Verification</h2>\n";
try {
    if (isset($user) && $user) {
        $testPassword = 'admin123';
        
        // Get the password hash
        $userWithPassword = $db->fetchOne(
            "SELECT password_hash FROM users WHERE id = ?",
            [$user['id']]
        );
        
        if ($userWithPassword) {
            $isValid = password_verify($testPassword, $userWithPassword['password_hash']);
            
            if ($isValid) {
                echo "<p>✅ Password verification successful</p>\n";
            } else {
                echo "<p>❌ Password verification failed</p>\n";
                echo "<p>Hash: " . substr($userWithPassword['password_hash'], 0, 20) . "...</p>\n";
            }
        } else {
            echo "<p>❌ Could not retrieve password hash</p>\n";
        }
    }
} catch (Exception $e) {
    echo "<p>❌ Password verification error: " . $e->getMessage() . "</p>\n";
}

// Test 6: Test JSON response function
echo "<h2>6. Testing JSON Response Function</h2>\n";
try {
    if (function_exists('jsonResponse')) {
        echo "<p>✅ jsonResponse function exists</p>\n";
        
        // Test JSON encoding
        $testData = ['test' => true, 'message' => 'Hello World'];
        $json = json_encode($testData);
        
        if ($json) {
            echo "<p>✅ JSON encoding works: " . $json . "</p>\n";
        } else {
            echo "<p>❌ JSON encoding failed</p>\n";
        }
    } else {
        echo "<p>❌ jsonResponse function missing</p>\n";
    }
} catch (Exception $e) {
    echo "<p>❌ JSON response error: " . $e->getMessage() . "</p>\n";
}

// Test 7: Test API tables
echo "<h2>7. Testing API Tables</h2>\n";
try {
    if (isset($db)) {
        // Check if api_tokens table exists
        $tables = $db->fetchAll("SHOW TABLES LIKE 'api_tokens'");
        
        if (count($tables) > 0) {
            echo "<p>✅ api_tokens table exists</p>\n";
            
            // Check table structure
            $columns = $db->fetchAll("DESCRIBE api_tokens");
            echo "<p>Table structure:</p>\n";
            echo "<ul>\n";
            foreach ($columns as $column) {
                echo "<li>" . $column['Field'] . " (" . $column['Type'] . ")</li>\n";
            }
            echo "</ul>\n";
        } else {
            echo "<p>❌ api_tokens table missing</p>\n";
            
            // Try to create it
            echo "<p>Attempting to create api_tokens table...</p>\n";
            
            $createSQL = "
            CREATE TABLE api_tokens (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                token VARCHAR(255) NOT NULL,
                expires_at TIMESTAMP NOT NULL,
                is_active TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_token (token),
                INDEX idx_user_id (user_id),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            
            $db->query($createSQL);
            echo "<p>✅ api_tokens table created</p>\n";
        }
    }
} catch (Exception $e) {
    echo "<p>❌ API tables error: " . $e->getMessage() . "</p>\n";
}

echo "<h2>🎯 Summary</h2>\n";
echo "<p>Debug complete. Check the results above to identify any issues.</p>\n";

echo "<h2>🔗 Next Steps</h2>\n";
echo "<ul>\n";
echo "<li><a href='../mobile-app/comprehensive-api-test.html'>Test API Integration</a></li>\n";
echo "<li><a href='auth.php?action=ping'>Test Auth Ping</a></li>\n";
echo "<li><a href='../mobile-app/index.html'>Test Mobile App</a></li>\n";
echo "</ul>\n";
?>
