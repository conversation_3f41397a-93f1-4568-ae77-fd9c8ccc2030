<?php
/**
 * Test Database Connection and User Setup
 */

echo "<h1>🔧 Database Test</h1>\n";
echo "<p>Testing database connection and user setup...</p>\n";

try {
    // Test 1: Load config
    echo "<h2>1. Loading Configuration</h2>\n";
    require_once 'config/config.php';
    echo "<p>✅ Config loaded successfully</p>\n";
    
    // Test 2: Check database connection
    echo "<h2>2. Testing Database Connection</h2>\n";
    if (isset($db)) {
        echo "<p>✅ Database object exists</p>\n";
        
        // Test simple query
        $result = $db->fetchOne("SELECT 1 as test");
        if ($result) {
            echo "<p>✅ Database connection working</p>\n";
        } else {
            echo "<p>❌ Database query failed</p>\n";
        }
    } else {
        echo "<p>❌ Database object not found</p>\n";
    }
    
    // Test 3: Check users table
    echo "<h2>3. Testing Users Table</h2>\n";
    $users = $db->fetchAll("SELECT id, username, email, role, is_active FROM users LIMIT 5");
    
    if ($users) {
        echo "<p>✅ Users table accessible</p>\n";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Role</th><th>Active</th></tr>\n";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . $user['username'] . "</td>";
            echo "<td>" . $user['email'] . "</td>";
            echo "<td>" . $user['role'] . "</td>";
            echo "<td>" . ($user['is_active'] ? 'Yes' : 'No') . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "<p>❌ No users found or table doesn't exist</p>\n";
    }
    
    // Test 4: Check admin user specifically
    echo "<h2>4. Testing Admin User</h2>\n";
    $admin = $db->fetchOne(
        "SELECT id, username, email, password_hash, role, is_active FROM users WHERE username = ?",
        ['admin']
    );
    
    if ($admin) {
        echo "<p>✅ Admin user found</p>\n";
        echo "<ul>\n";
        echo "<li><strong>ID:</strong> " . $admin['id'] . "</li>\n";
        echo "<li><strong>Username:</strong> " . $admin['username'] . "</li>\n";
        echo "<li><strong>Email:</strong> " . $admin['email'] . "</li>\n";
        echo "<li><strong>Role:</strong> " . $admin['role'] . "</li>\n";
        echo "<li><strong>Active:</strong> " . ($admin['is_active'] ? 'Yes' : 'No') . "</li>\n";
        echo "<li><strong>Password Hash:</strong> " . substr($admin['password_hash'], 0, 20) . "...</li>\n";
        echo "</ul>\n";
        
        // Test password verification
        echo "<h3>Password Verification Test</h3>\n";
        $testPassword = 'admin123';
        if (password_verify($testPassword, $admin['password_hash'])) {
            echo "<p>✅ Password 'admin123' verified successfully</p>\n";
        } else {
            echo "<p>❌ Password 'admin123' verification failed</p>\n";
            
            // Try to create a new password hash
            echo "<p>Creating new password hash for 'admin123'...</p>\n";
            $newHash = password_hash($testPassword, PASSWORD_DEFAULT);
            echo "<p>New hash: " . substr($newHash, 0, 30) . "...</p>\n";
            
            // Update the user's password
            $updated = $db->update('users', 
                ['password_hash' => $newHash], 
                'id = ?', 
                [$admin['id']]
            );
            
            if ($updated) {
                echo "<p>✅ Password updated successfully</p>\n";
            } else {
                echo "<p>❌ Failed to update password</p>\n";
            }
        }
    } else {
        echo "<p>❌ Admin user not found</p>\n";
        
        // Try to create admin user
        echo "<p>Creating admin user...</p>\n";
        $passwordHash = password_hash('admin123', PASSWORD_DEFAULT);
        
        $inserted = $db->insert('users', [
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password_hash' => $passwordHash,
            'full_name' => 'Administrator',
            'role' => 'admin',
            'is_active' => 1,
            'created_at' => date('Y-m-d H:i:s')
        ]);
        
        if ($inserted) {
            echo "<p>✅ Admin user created successfully</p>\n";
        } else {
            echo "<p>❌ Failed to create admin user</p>\n";
        }
    }
    
    // Test 5: Check api_tokens table
    echo "<h2>5. Testing API Tokens Table</h2>\n";
    $tables = $db->fetchAll("SHOW TABLES LIKE 'api_tokens'");
    
    if (count($tables) > 0) {
        echo "<p>✅ api_tokens table exists</p>\n";
        
        // Show table structure
        $columns = $db->fetchAll("DESCRIBE api_tokens");
        echo "<h3>Table Structure:</h3>\n";
        echo "<table border='1' style='border-collapse: collapse;'>\n";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>\n";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
        
        // Show recent tokens
        $tokens = $db->fetchAll("SELECT id, user_id, expires_at, is_active, created_at FROM api_tokens ORDER BY created_at DESC LIMIT 5");
        if ($tokens) {
            echo "<h3>Recent Tokens:</h3>\n";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
            echo "<tr><th>ID</th><th>User ID</th><th>Expires At</th><th>Active</th><th>Created At</th></tr>\n";
            foreach ($tokens as $token) {
                echo "<tr>";
                echo "<td>" . $token['id'] . "</td>";
                echo "<td>" . $token['user_id'] . "</td>";
                echo "<td>" . $token['expires_at'] . "</td>";
                echo "<td>" . ($token['is_active'] ? 'Yes' : 'No') . "</td>";
                echo "<td>" . $token['created_at'] . "</td>";
                echo "</tr>\n";
            }
            echo "</table>\n";
        }
    } else {
        echo "<p>❌ api_tokens table doesn't exist</p>\n";
        
        // Create the table
        echo "<p>Creating api_tokens table...</p>\n";
        $createSQL = "
        CREATE TABLE api_tokens (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            token VARCHAR(255) NOT NULL,
            expires_at TIMESTAMP NOT NULL,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_token (token),
            INDEX idx_user_id (user_id),
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $db->query($createSQL);
        echo "<p>✅ api_tokens table created</p>\n";
    }
    
    // Test 6: Test token generation
    echo "<h2>6. Testing Token Generation</h2>\n";
    $token = generateToken(32);
    if ($token && strlen($token) === 64) { // 32 bytes = 64 hex chars
        echo "<p>✅ Token generation working: " . substr($token, 0, 20) . "...</p>\n";
    } else {
        echo "<p>❌ Token generation failed</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>\n";
    echo "<p>File: " . $e->getFile() . "</p>\n";
    echo "<p>Line: " . $e->getLine() . "</p>\n";
}

echo "<h2>🎯 Summary</h2>\n";
echo "<p>Database test complete. Check results above for any issues.</p>\n";

echo "<h2>🔗 Next Steps</h2>\n";
echo "<ul>\n";
echo "<li><a href='mobile-app/fixed-auth-test.html'>Test Authentication</a></li>\n";
echo "<li><a href='api/test-auth-simple.php'>Simple Auth Test</a></li>\n";
echo "<li><a href='mobile-app/index.html'>Mobile App</a></li>\n";
echo "</ul>\n";
?>
