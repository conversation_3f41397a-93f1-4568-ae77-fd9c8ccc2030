<?php
/**
 * Clean Authentication API
 * Minimal implementation to fix JSON response issues
 */

// Disable error display to prevent HTML in JSON response
ini_set('display_errors', 0);
error_reporting(0);

// Set JSON headers first
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Start output buffering to catch any unwanted output
ob_start();

try {
    // Load configuration
    require_once dirname(__DIR__) . '/config/config.php';
    
    // Clear any output from config loading
    ob_clean();
    
    $method = $_SERVER['REQUEST_METHOD'];
    $action = $_GET['action'] ?? '';
    
    switch ($method) {
        case 'POST':
            if ($action === 'logout') {
                handleLogout();
            } elseif ($action === 'refresh') {
                handleRefreshToken();
            } else {
                handleLogin();
            }
            break;
            
        case 'GET':
            if ($action === 'verify') {
                handleVerifyToken();
            } elseif ($action === 'ping') {
                jsonResponse(['success' => true, 'message' => 'API is accessible', 'timestamp' => time()]);
            } else {
                jsonResponse(['error' => 'Invalid action'], 400);
            }
            break;
            
        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }
    
} catch (Exception $e) {
    // Clear any output
    ob_clean();
    
    // Log error for debugging
    error_log("Auth API Error: " . $e->getMessage());
    
    jsonResponse(['error' => 'Internal server error'], 500);
}

function handleLogin() {
    global $db;
    
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['username']) || !isset($input['password'])) {
        jsonResponse(['error' => 'Username and password required'], 400);
        return;
    }
    
    $username = trim($input['username']);
    $password = $input['password'];
    
    if (empty($username) || empty($password)) {
        jsonResponse(['error' => 'Username and password cannot be empty'], 400);
        return;
    }
    
    try {
        // Get user from database
        $user = $db->fetchOne(
            "SELECT id, username, email, password_hash, full_name, role, is_active 
             FROM users 
             WHERE (username = ? OR email = ?) AND is_active = 1",
            [$username, $username]
        );
        
        if (!$user || !password_verify($password, $user['password_hash'])) {
            jsonResponse(['error' => 'Invalid credentials'], 401);
            return;
        }
        
        // Generate API token
        $token = bin2hex(random_bytes(32)); // 64 character hex string
        $expiresAt = date('Y-m-d H:i:s', strtotime('+30 days'));
        
        // Ensure api_tokens table exists
        createApiTokensTable();
        
        // Store token in database
        $db->insert('api_tokens', [
            'user_id' => $user['id'],
            'token' => hash('sha256', $token),
            'expires_at' => $expiresAt,
            'is_active' => 1
        ]);
        
        // Update last login
        $db->update('users',
            ['last_login' => date('Y-m-d H:i:s')],
            'id = ?',
            [$user['id']]
        );
        
        // Remove password hash from response
        unset($user['password_hash']);
        
        jsonResponse([
            'success' => true,
            'token' => $token,
            'expires_at' => $expiresAt,
            'user' => $user
        ]);
        
    } catch (Exception $e) {
        error_log("Login error: " . $e->getMessage());
        jsonResponse(['error' => 'Database error'], 500);
    }
}

function handleLogout() {
    $token = getBearerToken();
    
    if ($token) {
        global $db;
        $hashedToken = hash('sha256', $token);
        
        try {
            $db->update('api_tokens',
                ['is_active' => 0],
                'token = ?',
                [$hashedToken]
            );
        } catch (Exception $e) {
            error_log("Logout error: " . $e->getMessage());
        }
    }
    
    jsonResponse(['success' => true, 'message' => 'Logged out successfully']);
}

function handleVerifyToken() {
    $user = authenticateRequest();
    
    if ($user) {
        jsonResponse([
            'success' => true,
            'user' => $user
        ]);
    } else {
        jsonResponse(['error' => 'Invalid token'], 401);
    }
}

function handleRefreshToken() {
    $user = authenticateRequest();
    
    if (!$user) {
        jsonResponse(['error' => 'Invalid token'], 401);
        return;
    }
    
    global $db;
    $oldToken = getBearerToken();
    $hashedOldToken = hash('sha256', $oldToken);
    
    try {
        // Generate new token
        $newToken = bin2hex(random_bytes(32));
        $expiresAt = date('Y-m-d H:i:s', strtotime('+30 days'));
        
        // Deactivate old token
        $db->update('api_tokens',
            ['is_active' => 0],
            'token = ?',
            [$hashedOldToken]
        );
        
        // Create new token
        $db->insert('api_tokens', [
            'user_id' => $user['id'],
            'token' => hash('sha256', $newToken),
            'expires_at' => $expiresAt,
            'is_active' => 1
        ]);
        
        jsonResponse([
            'success' => true,
            'token' => $newToken,
            'expires_at' => $expiresAt,
            'user' => $user
        ]);
        
    } catch (Exception $e) {
        error_log("Token refresh error: " . $e->getMessage());
        jsonResponse(['error' => 'Token refresh failed'], 500);
    }
}

function authenticateRequest() {
    global $db;
    
    $token = getBearerToken();
    
    if (!$token) {
        return false;
    }
    
    $hashedToken = hash('sha256', $token);
    
    try {
        // Get token and user info
        $result = $db->fetchOne(
            "SELECT u.id, u.username, u.email, u.full_name, u.role, u.is_active, t.expires_at
             FROM users u
             JOIN api_tokens t ON u.id = t.user_id
             WHERE t.token = ? AND t.is_active = 1 AND u.is_active = 1",
            [$hashedToken]
        );
        
        if (!$result) {
            return false;
        }
        
        // Check if token is expired
        if (strtotime($result['expires_at']) < time()) {
            // Deactivate expired token
            $db->update('api_tokens',
                ['is_active' => 0],
                'token = ?',
                [$hashedToken]
            );
            return false;
        }
        
        return $result;
        
    } catch (Exception $e) {
        error_log("Authentication error: " . $e->getMessage());
        return false;
    }
}

function getBearerToken() {
    $headers = null;
    
    // Try multiple methods to get authorization header
    if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers = $_SERVER['HTTP_AUTHORIZATION'];
    } elseif (isset($_SERVER['Authorization'])) {
        $headers = $_SERVER['Authorization'];
    } elseif (function_exists('apache_request_headers')) {
        $requestHeaders = apache_request_headers();
        $requestHeaders = array_change_key_case($requestHeaders, CASE_LOWER);
        if (isset($requestHeaders['authorization'])) {
            $headers = $requestHeaders['authorization'];
        }
    } elseif (function_exists('getallheaders')) {
        $allHeaders = getallheaders();
        $allHeaders = array_change_key_case($allHeaders, CASE_LOWER);
        if (isset($allHeaders['authorization'])) {
            $headers = $allHeaders['authorization'];
        }
    }
    
    if ($headers && preg_match('/Bearer\s+(.+)$/i', $headers, $matches)) {
        return trim($matches[1]);
    }
    
    return null;
}

function createApiTokensTable() {
    global $db;
    
    try {
        // Check if table exists
        $tables = $db->fetchAll("SHOW TABLES LIKE 'api_tokens'");
        
        if (count($tables) === 0) {
            // Create the table
            $createSQL = "
            CREATE TABLE api_tokens (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                token VARCHAR(255) NOT NULL,
                expires_at TIMESTAMP NOT NULL,
                is_active TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_token (token),
                INDEX idx_user_id (user_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            
            $db->query($createSQL);
        }
    } catch (Exception $e) {
        error_log("Table creation error: " . $e->getMessage());
    }
}

// Clean any remaining output and ensure JSON response
ob_end_clean();
?>
