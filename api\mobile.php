<?php
/**
 * Mobile API for Flori Construction Ltd Android App
 * Enhanced endpoints for mobile-specific functionality
 */

require_once dirname(__DIR__) . '/config/config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key, X-App-Version');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// API versioning and app compatibility
define('API_VERSION', '1.0');
define('MIN_ANDROID_VERSION', 24); // Android 7.0
define('MIN_APP_VERSION', '1.0.0');

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);
$action = $_GET['action'] ?? '';

// Check app version compatibility
checkAppCompatibility();

try {
    switch ($method) {
        case 'GET':
            handleGetRequest($action);
            break;
        case 'POST':
            handlePostRequest($action, $input);
            break;
        case 'PUT':
            handlePutRequest($action, $input);
            break;
        case 'DELETE':
            handleDeleteRequest($action);
            break;
        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }
} catch (Exception $e) {
    error_log("Mobile API Error: " . $e->getMessage());
    jsonResponse(['error' => 'Internal server error'], 500);
}

function handleGetRequest($action) {
    switch ($action) {
        case 'ping':
            jsonResponse([
                'success' => true,
                'message' => 'Mobile API is accessible',
                'version' => API_VERSION,
                'timestamp' => time(),
                'server_time' => date('Y-m-d H:i:s')
            ]);
            break;
        case 'dashboard':
            getDashboardData();
            break;
        case 'projects':
            getProjects();
            break;
        case 'project':
            getProject();
            break;
        case 'media':
            getMedia();
            break;
        case 'content':
            getContent();
            break;
        case 'sync':
            getSyncData();
            break;
        default:
            jsonResponse(['error' => 'Invalid action'], 400);
    }
}

function handlePostRequest($action, $input) {
    switch ($action) {
        case 'upload':
            handleFileUpload();
            break;
        case 'project':
            createProject($input);
            break;
        case 'sync':
            syncData($input);
            break;
        default:
            jsonResponse(['error' => 'Invalid action'], 400);
    }
}

function handlePutRequest($action, $input) {
    switch ($action) {
        case 'project':
            updateProject($input);
            break;
        case 'content':
            updateContent($input);
            break;
        case 'media':
            updateMedia($input);
            break;
        default:
            jsonResponse(['error' => 'Invalid action'], 400);
    }
}

function handleDeleteRequest($action) {
    switch ($action) {
        case 'project':
            deleteProject();
            break;
        case 'media':
            deleteMedia();
            break;
        default:
            jsonResponse(['error' => 'Invalid action'], 400);
    }
}

function getDashboardData() {
    $user = authenticateRequest();
    if (!$user) {
        jsonResponse(['error' => 'Unauthorized'], 401);
        return;
    }

    global $db;

    try {
        // Get dashboard statistics
        $stats = [
            'total_projects' => $db->fetchOne("SELECT COUNT(*) as count FROM projects WHERE is_active = 1")['count'],
            'completed_projects' => $db->fetchOne("SELECT COUNT(*) as count FROM projects WHERE project_type = 'completed' AND is_active = 1")['count'],
            'ongoing_projects' => $db->fetchOne("SELECT COUNT(*) as count FROM projects WHERE project_type = 'ongoing' AND is_active = 1")['count'],
            'featured_projects' => $db->fetchOne("SELECT COUNT(*) as count FROM projects WHERE is_featured = 1 AND is_active = 1")['count'],
            'total_media' => $db->fetchOne("SELECT COUNT(*) as count FROM media WHERE is_active = 1")['count'],
            'total_services' => $db->fetchOne("SELECT COUNT(*) as count FROM services WHERE is_active = 1")['count'],
            'total_testimonials' => $db->fetchOne("SELECT COUNT(*) as count FROM testimonials WHERE is_approved = 1")['count']
        ];

        // Get recent projects
        $recentProjects = $db->fetchAll(
            "SELECT id, title, project_type, featured_image, updated_at
             FROM projects
             WHERE is_active = 1
             ORDER BY updated_at DESC
             LIMIT 5"
        );

        // Get recent media uploads
        $recentMedia = $db->fetchAll(
            "SELECT id, filename, original_name, file_type, created_at
             FROM media
             WHERE is_active = 1
             ORDER BY created_at DESC
             LIMIT 10"
        );

        jsonResponse([
            'success' => true,
            'data' => [
                'stats' => $stats,
                'recent_projects' => $recentProjects,
                'recent_media' => $recentMedia,
                'last_sync' => date('Y-m-d H:i:s')
            ]
        ]);

    } catch (Exception $e) {
        error_log("Dashboard error: " . $e->getMessage());
        jsonResponse(['error' => 'Failed to load dashboard data'], 500);
    }
}

function getProjects() {
    $user = authenticateRequest();
    if (!$user) {
        jsonResponse(['error' => 'Unauthorized'], 401);
        return;
    }

    global $db;

    $page = (int)($_GET['page'] ?? 1);
    $limit = (int)($_GET['limit'] ?? 20);
    $type = $_GET['type'] ?? '';
    $search = $_GET['search'] ?? '';
    $featured = $_GET['featured'] ?? '';

    $offset = ($page - 1) * $limit;

    // Build query conditions
    $conditions = ['is_active = 1'];
    $params = [];

    if ($type) {
        $conditions[] = 'project_type = ?';
        $params[] = $type;
    }

    if ($search) {
        $conditions[] = '(title LIKE ? OR description LIKE ? OR location LIKE ?)';
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }

    if ($featured !== '') {
        $conditions[] = 'is_featured = ?';
        $params[] = (int)$featured;
    }

    $whereClause = implode(' AND ', $conditions);

    try {
        // Get total count
        $totalQuery = "SELECT COUNT(*) as count FROM projects WHERE $whereClause";
        $total = $db->fetchOne($totalQuery, $params)['count'];

        // Get projects
        $projectsQuery = "SELECT * FROM projects WHERE $whereClause ORDER BY sort_order ASC, updated_at DESC LIMIT $limit OFFSET $offset";
        $projects = $db->fetchAll($projectsQuery, $params);

        jsonResponse([
            'success' => true,
            'data' => [
                'projects' => $projects,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => (int)$total,
                    'pages' => ceil($total / $limit)
                ]
            ]
        ]);

    } catch (Exception $e) {
        error_log("Projects error: " . $e->getMessage());
        jsonResponse(['error' => 'Failed to load projects'], 500);
    }
}

function getProject() {
    $user = authenticateRequest();
    if (!$user) {
        jsonResponse(['error' => 'Unauthorized'], 401);
        return;
    }

    $id = $_GET['id'] ?? '';
    if (!$id) {
        jsonResponse(['error' => 'Project ID required'], 400);
        return;
    }

    global $db;

    try {
        error_log("Mobile API: Fetching project with ID: {$id}");

        $project = $db->fetchOne(
            "SELECT * FROM projects WHERE id = ? AND is_active = 1",
            [$id]
        );

        if (!$project) {
            error_log("Mobile API: Project not found for ID: {$id}");
            jsonResponse(['error' => 'Project not found'], 404);
            return;
        }

        error_log("Mobile API: Project found - Title: " . ($project['title'] ?? 'N/A'));

        // Get project media - handle gallery JSON safely
        $media = [];
        $gallery = $project['gallery'] ?? null;

        error_log("Mobile API: Processing gallery for project {$id}, gallery data: " . ($gallery ?? 'null'));

        if ($gallery && $gallery !== 'null' && $gallery !== '') {
            try {
                // Try to decode the gallery JSON
                $galleryIds = json_decode($gallery, true);

                if (json_last_error() !== JSON_ERROR_NONE) {
                    error_log("Mobile API: JSON decode error: " . json_last_error_msg());
                    error_log("Mobile API: Invalid gallery JSON: " . $gallery);
                } elseif (is_array($galleryIds) && !empty($galleryIds)) {
                    error_log("Mobile API: Gallery IDs found: " . implode(', ', $galleryIds));

                    // Create placeholders for IN clause
                    $placeholders = str_repeat('?,', count($galleryIds) - 1) . '?';
                    $media = $db->fetchAll(
                        "SELECT * FROM media WHERE id IN ($placeholders) AND is_active = 1 ORDER BY id",
                        $galleryIds
                    );

                    error_log("Mobile API: Found " . count($media) . " media items");
                } else {
                    error_log("Mobile API: Gallery IDs is not a valid array or is empty");
                }
            } catch (Exception $e) {
                // Log the error but don't fail the request
                error_log("Mobile API: Gallery processing exception for project {$project['id']}: " . $e->getMessage());
                error_log("Mobile API: Gallery data: " . $gallery);
            }
        } else {
            error_log("Mobile API: No gallery data for project {$id}");
        }

        error_log("Mobile API: Successfully returning project {$id} with " . count($media) . " media items");

        jsonResponse([
            'success' => true,
            'data' => [
                'project' => $project,
                'media' => $media
            ]
        ]);

    } catch (Exception $e) {
        error_log("Project error for ID {$id}: " . $e->getMessage());
        error_log("Project error stack trace: " . $e->getTraceAsString());
        jsonResponse(['error' => 'Failed to load project: ' . $e->getMessage()], 500);
    }
}

function checkAppCompatibility() {
    $appVersion = $_SERVER['HTTP_X_APP_VERSION'] ?? '';
    $androidVersion = $_SERVER['HTTP_X_ANDROID_VERSION'] ?? '';

    if ($appVersion && version_compare($appVersion, MIN_APP_VERSION, '<')) {
        jsonResponse([
            'error' => 'App update required',
            'min_version' => MIN_APP_VERSION,
            'current_version' => $appVersion
        ], 426);
        exit();
    }
}

function getMedia() {
    $user = authenticateRequest();
    if (!$user) {
        jsonResponse(['error' => 'Unauthorized'], 401);
        return;
    }

    global $db;

    $page = (int)($_GET['page'] ?? 1);
    $limit = (int)($_GET['limit'] ?? 20);
    $type = $_GET['type'] ?? '';
    $search = $_GET['search'] ?? '';

    $offset = ($page - 1) * $limit;

    // Build query conditions
    $conditions = ['is_active = 1'];
    $params = [];

    if ($type) {
        $conditions[] = 'file_type LIKE ?';
        $params[] = "$type%";
    }

    if ($search) {
        $conditions[] = '(filename LIKE ? OR original_name LIKE ? OR alt_text LIKE ?)';
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }

    $whereClause = implode(' AND ', $conditions);

    try {
        // Get total count
        $totalQuery = "SELECT COUNT(*) as count FROM media WHERE $whereClause";
        $total = $db->fetchOne($totalQuery, $params)['count'];

        // Get media
        $mediaQuery = "SELECT * FROM media WHERE $whereClause ORDER BY created_at DESC LIMIT $limit OFFSET $offset";
        $media = $db->fetchAll($mediaQuery, $params);

        jsonResponse([
            'success' => true,
            'data' => [
                'media' => $media,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => (int)$total,
                    'pages' => ceil($total / $limit)
                ]
            ]
        ]);

    } catch (Exception $e) {
        error_log("Media error: " . $e->getMessage());
        jsonResponse(['error' => 'Failed to load media'], 500);
    }
}

function handleFileUpload() {
    $user = authenticateRequest();
    if (!$user) {
        jsonResponse(['error' => 'Unauthorized'], 401);
        return;
    }

    if (!isset($_FILES['file'])) {
        jsonResponse(['error' => 'No file uploaded'], 400);
        return;
    }

    global $db;

    try {
        $file = $_FILES['file'];
        $altText = $_POST['alt_text'] ?? '';
        $caption = $_POST['caption'] ?? '';
        $projectId = $_POST['project_id'] ?? null;

        // Validate file
        $allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
        if (!in_array($file['type'], $allowedTypes)) {
            jsonResponse(['error' => 'Invalid file type'], 400);
            return;
        }

        $maxSize = 5 * 1024 * 1024; // 5MB
        if ($file['size'] > $maxSize) {
            jsonResponse(['error' => 'File too large'], 400);
            return;
        }

        // Generate unique filename
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = uniqid() . '_' . time() . '.' . $extension;
        $uploadPath = '../uploads/' . $filename;

        // Create upload directory if it doesn't exist
        $uploadDir = dirname($uploadPath);
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
            // Insert into database
            $mediaData = [
                'filename' => $filename,
                'original_name' => $file['name'],
                'file_path' => 'uploads/' . $filename,
                'file_type' => $file['type'],
                'file_size' => $file['size'],
                'mime_type' => $file['type'],
                'alt_text' => $altText,
                'caption' => $caption,
                'uploaded_by' => $user['id'],
                'created_at' => date('Y-m-d H:i:s')
            ];

            $mediaId = $db->insert('media', $mediaData);
            $mediaData['id'] = $mediaId;

            // If project_id is provided, add to project gallery
            if ($projectId) {
                $project = $db->fetchOne("SELECT gallery FROM projects WHERE id = ?", [$projectId]);
                if ($project) {
                    $gallery = json_decode($project['gallery'] ?? '[]', true);
                    $gallery[] = $mediaId;
                    $db->update('projects', ['gallery' => json_encode($gallery)], 'id = ?', [$projectId]);
                }
            }

            jsonResponse([
                'success' => true,
                'media' => $mediaData
            ]);
        } else {
            jsonResponse(['error' => 'Failed to upload file'], 500);
        }

    } catch (Exception $e) {
        error_log("Upload error: " . $e->getMessage());
        jsonResponse(['error' => 'Upload failed'], 500);
    }
}

function createProject($input) {
    $user = authenticateRequest();
    if (!$user) {
        jsonResponse(['error' => 'Unauthorized'], 401);
        return;
    }

    global $db;

    try {
        $projectData = [
            'title' => $input['title'] ?? '',
            'slug' => generateProjectSlug($input['title'] ?? ''),
            'description' => $input['description'] ?? '',
            'short_description' => $input['short_description'] ?? '',
            'client_name' => $input['client_name'] ?? '',
            'location' => $input['location'] ?? '',
            'project_type' => $input['project_type'] ?? 'completed',
            'start_date' => $input['start_date'] ?? null,
            'end_date' => $input['end_date'] ?? null,
            'project_value' => $input['project_value'] ?? null,
            'is_featured' => (int)($input['is_featured'] ?? 0),
            'sort_order' => (int)($input['sort_order'] ?? 0),
            'meta_title' => $input['meta_title'] ?? '',
            'meta_description' => $input['meta_description'] ?? '',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $projectId = $db->insert('projects', $projectData);
        $projectData['id'] = $projectId;

        jsonResponse([
            'success' => true,
            'data' => $projectData
        ]);

    } catch (Exception $e) {
        error_log("Create project error: " . $e->getMessage());
        jsonResponse(['error' => 'Failed to create project'], 500);
    }
}

function updateProject($input) {
    $user = authenticateRequest();
    if (!$user) {
        jsonResponse(['error' => 'Unauthorized'], 401);
        return;
    }

    $id = $input['id'] ?? '';
    if (!$id) {
        jsonResponse(['error' => 'Project ID required'], 400);
        return;
    }

    global $db;

    try {
        $projectData = [
            'title' => $input['title'] ?? '',
            'description' => $input['description'] ?? '',
            'short_description' => $input['short_description'] ?? '',
            'client_name' => $input['client_name'] ?? '',
            'location' => $input['location'] ?? '',
            'project_type' => $input['project_type'] ?? 'completed',
            'start_date' => $input['start_date'] ?? null,
            'end_date' => $input['end_date'] ?? null,
            'project_value' => $input['project_value'] ?? null,
            'is_featured' => (int)($input['is_featured'] ?? 0),
            'sort_order' => (int)($input['sort_order'] ?? 0),
            'meta_title' => $input['meta_title'] ?? '',
            'meta_description' => $input['meta_description'] ?? '',
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $db->update('projects', $projectData, 'id = ?', [$id]);

        // Get updated project
        $project = $db->fetchOne("SELECT * FROM projects WHERE id = ?", [$id]);

        jsonResponse([
            'success' => true,
            'data' => $project
        ]);

    } catch (Exception $e) {
        error_log("Update project error: " . $e->getMessage());
        jsonResponse(['error' => 'Failed to update project'], 500);
    }
}

function deleteProject() {
    $user = authenticateRequest();
    if (!$user) {
        jsonResponse(['error' => 'Unauthorized'], 401);
        return;
    }

    $id = $_GET['id'] ?? '';
    if (!$id) {
        jsonResponse(['error' => 'Project ID required'], 400);
        return;
    }

    global $db;

    try {
        $db->update('projects', ['is_active' => 0], 'id = ?', [$id]);

        jsonResponse([
            'success' => true,
            'message' => 'Project deleted successfully'
        ]);

    } catch (Exception $e) {
        error_log("Delete project error: " . $e->getMessage());
        jsonResponse(['error' => 'Failed to delete project'], 500);
    }
}

function getContent() {
    $user = authenticateRequest();
    if (!$user) {
        jsonResponse(['error' => 'Unauthorized'], 401);
        return;
    }

    global $db;

    try {
        // Get content from content table
        $content = [];
        $contentRows = $db->fetchAll("SELECT * FROM content WHERE is_active = 1");
        foreach ($contentRows as $row) {
            $key = $row['section_key'] ?? $row['slug'] ?? $row['id'];
            $content[$key] = $row;
        }

        // Get site content
        $siteContent = [];
        $siteContentRows = $db->fetchAll("SELECT * FROM site_content");
        foreach ($siteContentRows as $row) {
            $key = $row['content_key'] ?? $row['key'] ?? $row['id'];
            $siteContent[$key] = $row;
        }

        // Get site settings
        $siteSettings = [];
        $settingsRows = $db->fetchAll("SELECT * FROM site_settings");
        foreach ($settingsRows as $row) {
            $key = $row['setting_key'] ?? $row['key'] ?? $row['id'];
            $siteSettings[$key] = $row;
        }

        jsonResponse([
            'success' => true,
            'data' => [
                'content' => $content,
                'site_content' => $siteContent,
                'site_settings' => $siteSettings
            ]
        ]);

    } catch (Exception $e) {
        error_log("Content error: " . $e->getMessage());
        jsonResponse(['error' => 'Failed to load content'], 500);
    }
}

function updateContent($input) {
    $user = authenticateRequest();
    if (!$user) {
        jsonResponse(['error' => 'Unauthorized'], 401);
        return;
    }

    global $db;

    try {
        $type = $input['type'] ?? 'site_content';
        $key = $input['key'] ?? '';
        $value = $input['value'] ?? '';

        if (!$key) {
            jsonResponse(['error' => 'Key is required'], 400);
            return;
        }

        switch ($type) {
            case 'content':
                $title = $input['title'] ?? $key;
                $existing = $db->fetchOne("SELECT id FROM content WHERE slug = ?", [$key]);

                if ($existing) {
                    $db->update('content', [
                        'title' => $title,
                        'content' => $value,
                        'updated_at' => date('Y-m-d H:i:s')
                    ], 'slug = ?', [$key]);
                } else {
                    $db->insert('content', [
                        'title' => $title,
                        'slug' => $key,
                        'content' => $value,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                }
                break;

            case 'site_content':
                $existing = $db->fetchOne("SELECT id FROM site_content WHERE `key` = ?", [$key]);

                if ($existing) {
                    $db->update('site_content', [
                        'value' => $value,
                        'updated_at' => date('Y-m-d H:i:s')
                    ], '`key` = ?', [$key]);
                } else {
                    $db->insert('site_content', [
                        'key' => $key,
                        'value' => $value,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                }
                break;

            case 'site_settings':
                $existing = $db->fetchOne("SELECT id FROM site_settings WHERE `key` = ?", [$key]);

                if ($existing) {
                    $db->update('site_settings', [
                        'value' => $value,
                        'updated_at' => date('Y-m-d H:i:s')
                    ], '`key` = ?', [$key]);
                } else {
                    $db->insert('site_settings', [
                        'key' => $key,
                        'value' => $value,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                }
                break;

            default:
                jsonResponse(['error' => 'Invalid content type'], 400);
                return;
        }

        jsonResponse([
            'success' => true,
            'message' => 'Content updated successfully'
        ]);

    } catch (Exception $e) {
        error_log("Update content error: " . $e->getMessage());
        jsonResponse(['error' => 'Failed to update content'], 500);
    }
}

function updateMedia($input) {
    $user = authenticateRequest();
    if (!$user) {
        jsonResponse(['error' => 'Unauthorized'], 401);
        return;
    }

    $id = $input['id'] ?? '';
    if (!$id) {
        jsonResponse(['error' => 'Media ID required'], 400);
        return;
    }

    global $db;

    try {
        $mediaData = [
            'alt_text' => $input['alt_text'] ?? '',
            'caption' => $input['caption'] ?? '',
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $db->update('media', $mediaData, 'id = ?', [$id]);

        jsonResponse([
            'success' => true,
            'message' => 'Media updated successfully'
        ]);

    } catch (Exception $e) {
        error_log("Update media error: " . $e->getMessage());
        jsonResponse(['error' => 'Failed to update media'], 500);
    }
}

function deleteMedia() {
    $user = authenticateRequest();
    if (!$user) {
        jsonResponse(['error' => 'Unauthorized'], 401);
        return;
    }

    $id = $_GET['id'] ?? '';
    if (!$id) {
        jsonResponse(['error' => 'Media ID required'], 400);
        return;
    }

    global $db;

    try {
        // Get media info before deletion
        $media = $db->fetchOne("SELECT * FROM media WHERE id = ?", [$id]);

        if (!$media) {
            jsonResponse(['error' => 'Media not found'], 404);
            return;
        }

        // Soft delete
        $db->update('media', ['is_active' => 0], 'id = ?', [$id]);

        // Optionally delete physical file
        $filePath = '../uploads/' . $media['filename'];
        if (file_exists($filePath)) {
            unlink($filePath);
        }

        jsonResponse([
            'success' => true,
            'message' => 'Media deleted successfully'
        ]);

    } catch (Exception $e) {
        error_log("Delete media error: " . $e->getMessage());
        jsonResponse(['error' => 'Failed to delete media'], 500);
    }
}

function generateProjectSlug($title) {
    return strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $title), '-'));
}

// JSON response helper function is defined in config.php

// Authentication functions for mobile API
function authenticateRequest() {
    global $db;

    $token = getBearerToken();

    if (!$token) {
        return false;
    }

    $hashedToken = hash('sha256', $token);

    // Get token and user info
    $result = $db->fetchOne(
        "SELECT u.id, u.username, u.email, u.full_name, u.role, u.is_active, t.expires_at
         FROM users u
         JOIN api_tokens t ON u.id = t.user_id
         WHERE t.token = ? AND t.is_active = 1 AND u.is_active = 1",
        [$hashedToken]
    );

    if (!$result) {
        return false;
    }

    // Check if token is expired
    if (strtotime($result['expires_at']) < time()) {
        // Deactivate expired token
        $db->update('api_tokens',
            ['is_active' => 0],
            'token = ?',
            [$hashedToken]
        );
        return false;
    }

    return $result;
}

function getBearerToken() {
    $headers = getAuthorizationHeader();

    if (!empty($headers)) {
        if (preg_match('/Bearer\s(\S+)/', $headers, $matches)) {
            return $matches[1];
        }
    }

    return null;
}

function getAuthorizationHeader() {
    $headers = null;

    if (isset($_SERVER['Authorization'])) {
        $headers = trim($_SERVER["Authorization"]);
    } else if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers = trim($_SERVER["HTTP_AUTHORIZATION"]);
    } else if (function_exists('apache_request_headers')) {
        $requestHeaders = apache_request_headers();
        $requestHeaders = array_combine(array_map('ucwords', array_keys($requestHeaders)), array_values($requestHeaders));

        if (isset($requestHeaders['Authorization'])) {
            $headers = trim($requestHeaders['Authorization']);
        }
    }

    return $headers;
}

?>
