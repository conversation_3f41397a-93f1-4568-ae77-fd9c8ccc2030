<?php
/**
 * Test Authentication with cURL
 * Direct server-side test to see exact response
 */

echo "<h1>🔧 cURL Authentication Test</h1>\n";
echo "<p>Testing authentication endpoint directly from server...</p>\n";

// Test 1: Simple GET request to auth ping
echo "<h2>1. Testing Auth Ping</h2>\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/erdevwe/api/auth.php?action=ping');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "<p>❌ cURL Error: $error</p>\n";
} else {
    echo "<p>✅ HTTP Code: $httpCode</p>\n";
    echo "<pre>" . htmlspecialchars($response) . "</pre>\n";
}

// Test 2: POST login request
echo "<h2>2. Testing Login POST</h2>\n";
$loginData = json_encode([
    'username' => 'admin',
    'password' => 'admin123'
]);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/erdevwe/api/auth.php');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $loginData);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen($loginData)
]);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "<p>❌ cURL Error: $error</p>\n";
} else {
    echo "<p>✅ HTTP Code: $httpCode</p>\n";
    echo "<pre>" . htmlspecialchars($response) . "</pre>\n";
    
    // Try to extract JSON from response
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    $body = substr($response, $headerSize);
    
    echo "<h3>Response Body Only:</h3>\n";
    echo "<pre>" . htmlspecialchars($body) . "</pre>\n";
    
    // Try to decode JSON
    $jsonData = json_decode($body, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "<h3>Parsed JSON:</h3>\n";
        echo "<pre>" . print_r($jsonData, true) . "</pre>\n";
    } else {
        echo "<p>❌ JSON Parse Error: " . json_last_error_msg() . "</p>\n";
    }
}

// Test 3: Direct file inclusion test
echo "<h2>3. Testing Direct File Inclusion</h2>\n";
try {
    ob_start();
    
    // Simulate POST request
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_SERVER['CONTENT_TYPE'] = 'application/json';
    
    // Simulate JSON input
    $GLOBALS['HTTP_RAW_POST_DATA'] = json_encode([
        'username' => 'admin',
        'password' => 'admin123'
    ]);
    
    // Capture any output
    include 'api/auth.php';
    
    $output = ob_get_clean();
    
    echo "<p>✅ File included successfully</p>\n";
    echo "<h3>Output:</h3>\n";
    echo "<pre>" . htmlspecialchars($output) . "</pre>\n";
    
} catch (Exception $e) {
    ob_end_clean();
    echo "<p>❌ Include Error: " . $e->getMessage() . "</p>\n";
}

echo "<h2>🎯 Summary</h2>\n";
echo "<p>Check the outputs above to identify the exact issue with authentication.</p>\n";
?>
