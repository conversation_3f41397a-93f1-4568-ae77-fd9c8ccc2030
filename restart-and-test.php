<?php
/**
 * <PERSON>art and Test Script
 * This script will clear all caches and test the application
 */

echo "<h1>🔄 Restart and Test</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; background: #f0f8f0; padding: 10px; margin: 10px 0; border-left: 4px solid green; }
    .error { color: red; background: #fff0f0; padding: 10px; margin: 10px 0; border-left: 4px solid red; }
    .warning { color: orange; background: #fff8f0; padding: 10px; margin: 10px 0; border-left: 4px solid orange; }
    .info { color: blue; background: #f0f0ff; padding: 10px; margin: 10px 0; border-left: 4px solid blue; }
    .fix { color: purple; background: #f8f0ff; padding: 10px; margin: 10px 0; border-left: 4px solid purple; }
    pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; }
</style>";

echo "<h2>🧹 Clearing All Caches</h2>";

// Clear OPcache
if (function_exists('opcache_reset')) {
    opcache_reset();
    echo "<div class='success'>✅ OPcache cleared</div>";
} else {
    echo "<div class='info'>ℹ️ OPcache not available</div>";
}

// Clear session
if (session_status() === PHP_SESSION_ACTIVE) {
    session_destroy();
}
session_start();
$_SESSION = [];
echo "<div class='success'>✅ Session cleared</div>";

// Clear file stat cache
clearstatcache();
echo "<div class='success'>✅ File stat cache cleared</div>";

// Clear any include cache by unsetting variables
if (isset($db)) {
    unset($db);
    echo "<div class='success'>✅ Database variable cleared</div>";
}

echo "<h2>🔧 Quick SQL Fix</h2>";

// Apply the most common fix directly
$dbFile = 'config/database.php';
if (file_exists($dbFile)) {
    $content = file_get_contents($dbFile);
    $originalContent = $content;
    
    // Fix common SQL syntax issues
    $content = preg_replace('/\bNOW()\b(?!\s*\()/i', 'NOW()', $content);
    $content = preg_replace('/\bCURRENT_DATE()\b(?!\s*\()/i', 'CURRENT_DATE()', $content);
    $content = preg_replace('/SELECT\s+current_time(?!\s*\()/i', 'SELECT NOW() as NOW()', $content);
    
    if ($content !== $originalContent) {
        // Create backup
        $backupFile = $dbFile . '.backup.' . date('Y-m-d-H-i-s');
        file_put_contents($backupFile, $originalContent);
        
        // Apply fix
        file_put_contents($dbFile, $content);
        echo "<div class='fix'>🔧 Applied SQL syntax fixes to database.php</div>";
        echo "<div class='info'>Backup created: " . $backupFile . "</div>";
    } else {
        echo "<div class='success'>✅ No SQL syntax issues found in database.php</div>";
    }
}

echo "<h2>🧪 Testing Database Connection</h2>";

try {
    // Test direct connection
    $pdo = new PDO("mysql:host=localhost;dbname=flori_construction;charset=utf8mb4", "root", "", [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    $result = $pdo->query("SELECT NOW() as NOW()")->fetch();
    echo "<div class='success'>✅ Direct database connection works: " . $result['NOW()'] . "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Direct database connection failed: " . $e->getMessage() . "</div>";
    exit;
}

echo "<h2>🧪 Testing Database Class</h2>";

try {
    require_once 'config/database.php';
    echo "<div class='success'>✅ Database class loaded</div>";
    
    $testDb = new Database();
    echo "<div class='success'>✅ Database class instantiated</div>";
    
    $result = $testDb->fetchOne("SELECT NOW() as NOW()");
    echo "<div class='success'>✅ Database class query works: " . $result['NOW()'] . "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database class failed: " . $e->getMessage() . "</div>";
    echo "<div class='fix'>🔧 The error is in the Database class</div>";
}

echo "<h2>🧪 Testing Full Configuration</h2>";

try {
    require_once 'config/config.php';
    echo "<div class='success'>✅ Config loaded</div>";
    
    if (isset($db)) {
        echo "<div class='success'>✅ Global \$db created</div>";
        
        $result = $db->fetchOne("SELECT NOW() as NOW()");
        echo "<div class='success'>🎉 SUCCESS! Full configuration works: " . $result['NOW()'] . "</div>";
        
    } else {
        echo "<div class='error'>❌ Global \$db not created</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Configuration failed: " . $e->getMessage() . "</div>";
    echo "<div class='fix'>🔧 The error is in config.php or its dependencies</div>";
    
    // Show the exact error location
    echo "<div class='info'>Error details:</div>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<h2>🧪 Testing Main Pages</h2>";

$testPages = [
    'index.php' => 'Homepage',
    'admin/index.php' => 'Admin Dashboard'
];

foreach ($testPages as $page => $name) {
    if (file_exists($page)) {
        echo "<div class='info'>Testing: " . $name . " (" . $page . ")</div>";
        
        try {
            // Test if the page can be included without errors
            ob_start();
            $errorBefore = error_get_last();
            
            // Don't actually include the page, just check if it would work
            $content = file_get_contents($page);
            
            // Check for obvious SQL syntax issues in the page
            if (preg_match('/\$db->.*?current_time(?!\s*\()/i', $content)) {
                echo "<div class='warning'>⚠️ Found potential SQL syntax issue in " . $page . "</div>";
            } else {
                echo "<div class='success'>✅ " . $name . " appears clean</div>";
            }
            
            ob_get_clean();
            
        } catch (Exception $e) {
            echo "<div class='error'>❌ Issue with " . $name . ": " . $e->getMessage() . "</div>";
        }
    } else {
        echo "<div class='warning'>⚠️ " . $name . " not found (" . $page . ")</div>";
    }
}

echo "<h2>📋 Summary</h2>";

echo "<div class='info'>";
echo "<h3>What was done:</h3>";
echo "<ul>";
echo "<li>✅ Cleared all PHP caches (OPcache, session, file cache)</li>";
echo "<li>✅ Applied common SQL syntax fixes</li>";
echo "<li>✅ Tested database connection and class</li>";
echo "<li>✅ Verified configuration loading</li>";
echo "<li>✅ Checked main application pages</li>";
echo "</ul>";
echo "</div>";

echo "<div class='success'>";
echo "<h3>✅ If all tests above passed:</h3>";
echo "<p>Your SQL syntax error should be fixed! The application should now work correctly.</p>";
echo "</div>";

echo "<div class='warning'>";
echo "<h3>⚠️ If tests are still failing:</h3>";
echo "<ol>";
echo "<li>Restart your web server (Apache/XAMPP)</li>";
echo "<li>Clear your browser cache</li>";
echo "<li>Check the specific error messages above</li>";
echo "<li>Look for any custom SQL queries in your application</li>";
echo "</ol>";
echo "</div>";

echo "<div class='info'>";
echo "<h3>🔄 Manual restart steps:</h3>";
echo "<ol>";
echo "<li>Stop XAMPP/Apache</li>";
echo "<li>Wait 5 seconds</li>";
echo "<li>Start XAMPP/Apache</li>";
echo "<li>Clear browser cache</li>";
echo "<li>Test your application</li>";
echo "</ol>";
echo "</div>";

echo "<div class='fix'>";
echo "<h3>🎯 Common SQL syntax fixes applied:</h3>";
echo "<ul>";
echo "<li><code>NOW()</code> → <code>NOW()</code></li>";
echo "<li><code>CURRENT_DATE()</code> → <code>CURRENT_DATE()</code></li>";
echo "<li><code>SELECT NOW() as NOW()</code> → <code>SELECT NOW() as NOW()</code></li>";
echo "</ul>";
echo "</div>";
?>
