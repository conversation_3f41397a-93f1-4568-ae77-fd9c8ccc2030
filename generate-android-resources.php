<?php
/**
 * Generate Android Icons and Splash Screens for Flori Construction Admin
 * This script creates all required Android resources from the PWA icons
 */

// Set memory limit for image processing
ini_set('memory_limit', '256M');

// Check if GD extension is available
if (!extension_loaded('gd')) {
    die("Error: GD extension is not available. Please install php-gd.\n");
}

// Define paths
$sourceIconPath = '../icons/icon-512x512.png';
$androidIconDir = 'res/android/icon';
$androidSplashDir = 'res/android/splash';

// Create directories if they don't exist
if (!is_dir($androidIconDir)) {
    mkdir($androidIconDir, 0755, true);
}
if (!is_dir($androidSplashDir)) {
    mkdir($androidSplashDir, 0755, true);
}

// Android icon sizes and filenames
$iconSizes = [
    36 => 'drawable-ldpi-icon.png',
    48 => 'drawable-mdpi-icon.png',
    72 => 'drawable-hdpi-icon.png',
    96 => 'drawable-xhdpi-icon.png',
    144 => 'drawable-xxhdpi-icon.png',
    192 => 'drawable-xxxhdpi-icon.png'
];

// Android splash screen sizes and filenames
$splashSizes = [
    // Portrait
    'port-ldpi' => ['width' => 200, 'height' => 320, 'filename' => 'drawable-port-ldpi-screen.png'],
    'port-mdpi' => ['width' => 320, 'height' => 480, 'filename' => 'drawable-port-mdpi-screen.png'],
    'port-hdpi' => ['width' => 480, 'height' => 800, 'filename' => 'drawable-port-hdpi-screen.png'],
    'port-xhdpi' => ['width' => 720, 'height' => 1280, 'filename' => 'drawable-port-xhdpi-screen.png'],
    'port-xxhdpi' => ['width' => 960, 'height' => 1600, 'filename' => 'drawable-port-xxhdpi-screen.png'],
    'port-xxxhdpi' => ['width' => 1280, 'height' => 1920, 'filename' => 'drawable-port-xxxhdpi-screen.png'],
    
    // Landscape
    'land-ldpi' => ['width' => 320, 'height' => 200, 'filename' => 'drawable-land-ldpi-screen.png'],
    'land-mdpi' => ['width' => 480, 'height' => 320, 'filename' => 'drawable-land-mdpi-screen.png'],
    'land-hdpi' => ['width' => 800, 'height' => 480, 'filename' => 'drawable-land-hdpi-screen.png'],
    'land-xhdpi' => ['width' => 1280, 'height' => 720, 'filename' => 'drawable-land-xhdpi-screen.png'],
    'land-xxhdpi' => ['width' => 1600, 'height' => 960, 'filename' => 'drawable-land-xxhdpi-screen.png'],
    'land-xxxhdpi' => ['width' => 1920, 'height' => 1280, 'filename' => 'drawable-land-xxxhdpi-screen.png']
];

// Brand colors
$brandColor = '#e74c3c'; // Flori Construction red

echo "🎨 Generating Android Resources for Flori Construction Admin\n";
echo "===========================================================\n\n";

// Check if source icon exists
if (!file_exists($sourceIconPath)) {
    echo "❌ Source icon not found: $sourceIconPath\n";
    echo "Creating a basic icon...\n";
    
    // Create a basic icon if source doesn't exist
    $sourceIcon = createBasicIcon(512);
    imagepng($sourceIcon, $sourceIconPath);
    imagedestroy($sourceIcon);
    echo "✅ Basic icon created\n";
}

// Load source icon
$sourceIcon = imagecreatefrompng($sourceIconPath);
if (!$sourceIcon) {
    die("❌ Failed to load source icon: $sourceIconPath\n");
}

echo "📱 Generating Android Icons...\n";

// Generate Android icons
foreach ($iconSizes as $size => $filename) {
    echo "  Creating {$filename} ({$size}x{$size})... ";
    
    $icon = imagecreatetruecolor($size, $size);
    
    // Enable alpha blending
    imagealphablending($icon, false);
    imagesavealpha($icon, true);
    
    // Create transparent background
    $transparent = imagecolorallocatealpha($icon, 0, 0, 0, 127);
    imagefill($icon, 0, 0, $transparent);
    
    // Resize source icon
    imagecopyresampled($icon, $sourceIcon, 0, 0, 0, 0, $size, $size, 512, 512);
    
    // Save icon
    $filepath = $androidIconDir . '/' . $filename;
    if (imagepng($icon, $filepath)) {
        echo "✅\n";
    } else {
        echo "❌\n";
    }
    
    imagedestroy($icon);
}

echo "\n🖼️  Generating Android Splash Screens...\n";

// Generate Android splash screens
foreach ($splashSizes as $key => $config) {
    $width = $config['width'];
    $height = $config['height'];
    $filename = $config['filename'];
    
    echo "  Creating {$filename} ({$width}x{$height})... ";
    
    $splash = createSplashScreen($width, $height, $sourceIcon);
    
    // Save splash screen
    $filepath = $androidSplashDir . '/' . $filename;
    if (imagepng($splash, $filepath)) {
        echo "✅\n";
    } else {
        echo "❌\n";
    }
    
    imagedestroy($splash);
}

// Clean up
imagedestroy($sourceIcon);

echo "\n✅ Android resources generated successfully!\n";
echo "\n📁 Generated files:\n";
echo "Icons: " . count($iconSizes) . " files in $androidIconDir/\n";
echo "Splash screens: " . count($splashSizes) . " files in $androidSplashDir/\n";

/**
 * Create a basic icon with FC branding
 */
function createBasicIcon($size) {
    global $brandColor;
    
    $icon = imagecreatetruecolor($size, $size);
    
    // Enable alpha blending
    imagealphablending($icon, false);
    imagesavealpha($icon, true);
    
    // Create gradient background
    $centerX = $size / 2;
    $centerY = $size / 2;
    $radius = $size / 2;
    
    for ($x = 0; $x < $size; $x++) {
        for ($y = 0; $y < $size; $y++) {
            $distance = sqrt(pow($x - $centerX, 2) + pow($y - $centerY, 2));
            
            if ($distance <= $radius) {
                $alpha = 1 - ($distance / $radius) * 0.3;
                $red = hexdec(substr($brandColor, 1, 2));
                $green = hexdec(substr($brandColor, 3, 2));
                $blue = hexdec(substr($brandColor, 5, 2));
                
                $color = imagecolorallocatealpha($icon, $red, $green, $blue, (1 - $alpha) * 127);
                imagesetpixel($icon, $x, $y, $color);
            } else {
                $transparent = imagecolorallocatealpha($icon, 0, 0, 0, 127);
                imagesetpixel($icon, $x, $y, $transparent);
            }
        }
    }
    
    // Add "FC" text
    $white = imagecolorallocate($icon, 255, 255, 255);
    $fontSize = $size / 8;
    $text = 'FC';
    
    // Calculate text position (center)
    $textBox = imagettfbbox($fontSize, 0, __DIR__ . '/arial.ttf', $text);
    if (!$textBox) {
        // Fallback to imagestring if TTF not available
        $textWidth = strlen($text) * imagefontwidth(5);
        $textHeight = imagefontheight(5);
        $x = ($size - $textWidth) / 2;
        $y = ($size - $textHeight) / 2;
        imagestring($icon, 5, $x, $y, $text, $white);
    } else {
        $textWidth = $textBox[4] - $textBox[0];
        $textHeight = $textBox[1] - $textBox[5];
        $x = ($size - $textWidth) / 2;
        $y = ($size - $textHeight) / 2 + $textHeight;
        imagettftext($icon, $fontSize, 0, $x, $y, $white, __DIR__ . '/arial.ttf', $text);
    }
    
    return $icon;
}

/**
 * Create splash screen with logo
 */
function createSplashScreen($width, $height, $sourceIcon) {
    global $brandColor;
    
    $splash = imagecreatetruecolor($width, $height);
    
    // Create gradient background
    $red = hexdec(substr($brandColor, 1, 2));
    $green = hexdec(substr($brandColor, 3, 2));
    $blue = hexdec(substr($brandColor, 5, 2));
    
    // Create gradient from brand color to darker shade
    for ($y = 0; $y < $height; $y++) {
        $ratio = $y / $height;
        $currentRed = $red * (1 - $ratio * 0.3);
        $currentGreen = $green * (1 - $ratio * 0.3);
        $currentBlue = $blue * (1 - $ratio * 0.3);
        
        $color = imagecolorallocate($splash, $currentRed, $currentGreen, $currentBlue);
        imageline($splash, 0, $y, $width, $y, $color);
    }
    
    // Add logo in center
    $logoSize = min($width, $height) * 0.3; // 30% of smallest dimension
    $logoX = ($width - $logoSize) / 2;
    $logoY = ($height - $logoSize) / 2;
    
    // Create logo
    $logo = imagecreatetruecolor($logoSize, $logoSize);
    imagealphablending($logo, false);
    imagesavealpha($logo, true);
    
    // Resize source icon for logo
    imagecopyresampled($logo, $sourceIcon, 0, 0, 0, 0, $logoSize, $logoSize, 512, 512);
    
    // Add logo to splash
    imagecopy($splash, $logo, $logoX, $logoY, 0, 0, $logoSize, $logoSize);
    
    // Add app name below logo
    $white = imagecolorallocate($splash, 255, 255, 255);
    $text = 'Flori Construction Admin';
    $fontSize = max(12, $width / 40);
    
    // Calculate text position
    $textWidth = strlen($text) * imagefontwidth(3);
    $textX = ($width - $textWidth) / 2;
    $textY = $logoY + $logoSize + 20;
    
    imagestring($splash, 3, $textX, $textY, $text, $white);
    
    imagedestroy($logo);
    
    return $splash;
}

echo "\n🚀 Ready to build Android app!\n";
echo "Run: ./build.sh or build.bat\n";
?>
