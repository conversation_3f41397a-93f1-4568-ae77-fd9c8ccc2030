<?php
/**
 * Unified Authentication Helper for Flori Construction API
 * Provides consistent authentication functions across all API endpoints
 */

/**
 * Authenticate API request using Bearer token
 * @return array|false User data if authenticated, false otherwise
 */
function authenticateRequest() {
    global $db;

    $token = getBearerToken();

    if (!$token) {
        error_log("Auth: No bearer token found");
        return false;
    }

    $hashedToken = hash('sha256', $token);

    try {
        // Get token and user info
        $result = $db->fetchOne(
            "SELECT u.id, u.username, u.email, u.full_name, u.role, u.is_active, t.expires_at
             FROM users u
             JOIN api_tokens t ON u.id = t.user_id
             WHERE t.token = ? AND t.is_active = 1 AND u.is_active = 1",
            [$hashedToken]
        );

        if (!$result) {
            error_log("Auth: Token not found in database or user inactive");
            return false;
        }

        // Check if token is expired
        if (strtotime($result['expires_at']) < time()) {
            error_log("Auth: Token expired for user " . $result['username']);
            
            // Deactivate expired token
            $db->update('api_tokens',
                ['is_active' => 0],
                'token = ?',
                [$hashedToken]
            );
            return false;
        }

        error_log("Auth: Successfully authenticated user " . $result['username']);
        return $result;

    } catch (Exception $e) {
        error_log("Auth: Database error during authentication: " . $e->getMessage());
        return false;
    }
}

/**
 * Extract Bearer token from Authorization header
 * @return string|null Token if found, null otherwise
 */
function getBearerToken() {
    $headers = getAuthorizationHeader();

    if (!empty($headers)) {
        // Match Bearer token pattern (case insensitive)
        if (preg_match('/Bearer\s+(.+)$/i', $headers, $matches)) {
            $token = trim($matches[1]);
            error_log("Auth: Found bearer token: " . substr($token, 0, 10) . "...");
            return $token;
        }
    }

    error_log("Auth: No valid bearer token found in headers");
    return null;
}

/**
 * Get Authorization header from various sources
 * @return string|null Authorization header value
 */
function getAuthorizationHeader() {
    $headers = null;

    // Method 1: Direct server variable
    if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers = trim($_SERVER['HTTP_AUTHORIZATION']);
        error_log("Auth: Found authorization header via HTTP_AUTHORIZATION");
    }
    // Method 2: Alternative server variable
    else if (isset($_SERVER['Authorization'])) {
        $headers = trim($_SERVER['Authorization']);
        error_log("Auth: Found authorization header via Authorization");
    }
    // Method 3: Apache request headers function
    else if (function_exists('apache_request_headers')) {
        $requestHeaders = apache_request_headers();
        
        // Make headers case-insensitive
        $requestHeaders = array_change_key_case($requestHeaders, CASE_LOWER);
        
        if (isset($requestHeaders['authorization'])) {
            $headers = trim($requestHeaders['authorization']);
            error_log("Auth: Found authorization header via apache_request_headers");
        }
    }
    // Method 4: getallheaders function (if available)
    else if (function_exists('getallheaders')) {
        $allHeaders = getallheaders();
        
        // Make headers case-insensitive
        $allHeaders = array_change_key_case($allHeaders, CASE_LOWER);
        
        if (isset($allHeaders['authorization'])) {
            $headers = trim($allHeaders['authorization']);
            error_log("Auth: Found authorization header via getallheaders");
        }
    }

    if (!$headers) {
        error_log("Auth: No authorization header found");
        error_log("Auth: Available server vars: " . implode(', ', array_keys($_SERVER)));
    }

    return $headers;
}

/**
 * Generate a secure random token for API authentication
 * @param int $length Token length
 * @return string Generated token
 */
function generateApiToken($length = 64) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * Validate token format
 * @param string $token Token to validate
 * @return bool True if valid format
 */
function isValidTokenFormat($token) {
    // Token should be hexadecimal and at least 32 characters
    return preg_match('/^[a-f0-9]{32,}$/i', $token);
}

/**
 * Log authentication attempt
 * @param string $username Username
 * @param bool $success Success status
 * @param string $ip IP address
 */
function logAuthAttempt($username, $success, $ip = null) {
    if (!$ip) {
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    $status = $success ? 'SUCCESS' : 'FAILED';
    error_log("Auth: Login attempt for '$username' from $ip: $status");
}

/**
 * Check if request is from mobile app
 * @return bool True if from mobile app
 */
function isMobileAppRequest() {
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $referer = $_SERVER['HTTP_REFERER'] ?? '';
    
    return strpos($referer, '/mobile-app/') !== false || 
           strpos($userAgent, 'FloriAdmin') !== false;
}

/**
 * Get client IP address
 * @return string Client IP
 */
function getClientIP() {
    $ipKeys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}

/**
 * Rate limiting for authentication attempts
 * @param string $identifier IP or username
 * @param int $maxAttempts Maximum attempts allowed
 * @param int $timeWindow Time window in seconds
 * @return bool True if within limits
 */
function checkRateLimit($identifier, $maxAttempts = 5, $timeWindow = 300) {
    global $db;
    
    try {
        $since = date('Y-m-d H:i:s', time() - $timeWindow);
        
        $attempts = $db->fetchOne(
            "SELECT COUNT(*) as count FROM auth_attempts 
             WHERE identifier = ? AND attempted_at > ? AND success = 0",
            [$identifier, $since]
        );
        
        return ($attempts['count'] ?? 0) < $maxAttempts;
        
    } catch (Exception $e) {
        error_log("Auth: Rate limit check failed: " . $e->getMessage());
        return true; // Allow on error
    }
}

/**
 * Record authentication attempt
 * @param string $identifier IP or username
 * @param bool $success Success status
 */
function recordAuthAttempt($identifier, $success) {
    global $db;
    
    try {
        $db->insert('auth_attempts', [
            'identifier' => $identifier,
            'success' => $success ? 1 : 0,
            'attempted_at' => date('Y-m-d H:i:s'),
            'ip_address' => getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    } catch (Exception $e) {
        error_log("Auth: Failed to record auth attempt: " . $e->getMessage());
    }
}

/**
 * Clean up old authentication attempts
 */
function cleanupAuthAttempts() {
    global $db;
    
    try {
        $cutoff = date('Y-m-d H:i:s', time() - 86400); // 24 hours ago
        $db->query("DELETE FROM auth_attempts WHERE attempted_at < ?", [$cutoff]);
    } catch (Exception $e) {
        error_log("Auth: Failed to cleanup auth attempts: " . $e->getMessage());
    }
}

/**
 * Verify API endpoint access permissions
 * @param array $user User data
 * @param string $endpoint Endpoint name
 * @param string $action Action being performed
 * @return bool True if allowed
 */
function checkEndpointPermission($user, $endpoint, $action = 'read') {
    // Admin users have full access
    if ($user['role'] === 'admin') {
        return true;
    }
    
    // Define permissions for different roles
    $permissions = [
        'editor' => [
            'projects' => ['read', 'create', 'update'],
            'services' => ['read', 'create', 'update'],
            'content' => ['read', 'create', 'update'],
            'media' => ['read', 'create', 'update'],
            'mobile' => ['read']
        ],
        'viewer' => [
            'projects' => ['read'],
            'services' => ['read'],
            'content' => ['read'],
            'media' => ['read'],
            'mobile' => ['read']
        ]
    ];
    
    $userRole = $user['role'] ?? 'viewer';
    $allowedActions = $permissions[$userRole][$endpoint] ?? [];
    
    return in_array($action, $allowedActions);
}
?>
