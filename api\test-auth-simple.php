<?php
/**
 * Simple Authentication Test
 * Direct test without complex HTML
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set JSON header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

try {
    // Test 1: Load config
    require_once dirname(__DIR__) . '/config/config.php';
    
    // Test 2: Load auth helper
    require_once __DIR__ . '/auth-helper.php';
    
    // Test 3: Check if we have a database connection
    if (!isset($db)) {
        throw new Exception('Database connection not available');
    }
    
    // Test 4: Check if admin user exists
    $user = $db->fetchOne(
        "SELECT id, username, email, password_hash, full_name, role, is_active FROM users WHERE username = ? AND is_active = 1",
        ['admin']
    );
    
    if (!$user) {
        throw new Exception('Admin user not found');
    }
    
    // Test 5: Verify password
    $testPassword = 'admin123';
    if (!password_verify($testPassword, $user['password_hash'])) {
        throw new Exception('Password verification failed');
    }
    
    // Test 6: Generate token
    $token = generateToken(32);
    if (!$token) {
        throw new Exception('Token generation failed');
    }
    
    // Test 7: Create API token record
    $expiresAt = date('Y-m-d H:i:s', strtotime('+30 days'));
    
    // Check if api_tokens table exists
    $tables = $db->fetchAll("SHOW TABLES LIKE 'api_tokens'");
    if (count($tables) === 0) {
        // Create the table
        $createSQL = "
        CREATE TABLE api_tokens (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            token VARCHAR(255) NOT NULL,
            expires_at TIMESTAMP NOT NULL,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_token (token),
            INDEX idx_user_id (user_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $db->query($createSQL);
    }
    
    // Insert token
    $db->insert('api_tokens', [
        'user_id' => $user['id'],
        'token' => hash('sha256', $token),
        'expires_at' => $expiresAt,
        'is_active' => 1
    ]);
    
    // Remove password hash from response
    unset($user['password_hash']);
    
    // Success response
    jsonResponse([
        'success' => true,
        'message' => 'Authentication test successful',
        'token' => $token,
        'expires_at' => $expiresAt,
        'user' => $user,
        'tests_passed' => [
            'config_loaded' => true,
            'auth_helper_loaded' => true,
            'database_connected' => true,
            'user_found' => true,
            'password_verified' => true,
            'token_generated' => true,
            'token_stored' => true
        ]
    ]);
    
} catch (Exception $e) {
    // Error response
    jsonResponse([
        'success' => false,
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ], 500);
}
?>
