<?php
/**
 * Test Project API Endpoint
 * This script tests the mobile.php API endpoint for project retrieval
 */

require_once 'config/config.php';

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🧪 Project API Test</h1>";

// Test project IDs
$testProjectIds = [7, 8, 1, 2, 3];

echo "<style>
    body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }
    .test-section { background: #f8f9fa; padding: 15px; margin: 15px 0; border-radius: 5px; border-left: 4px solid #007bff; }
    .success { border-left-color: #28a745; background: #d4edda; }
    .error { border-left-color: #dc3545; background: #f8d7da; }
    .warning { border-left-color: #ffc107; background: #fff3cd; }
    pre { background: #f1f1f1; padding: 10px; border-radius: 3px; overflow-x: auto; }
    .project-info { background: white; padding: 10px; margin: 10px 0; border-radius: 3px; }
</style>";

// Function to test database connection
function testDatabaseConnection() {
    global $db;
    try {
        $result = $db->fetchOne("SELECT COUNT(*) as count FROM projects");
        return ['success' => true, 'count' => $result['count']];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// Function to test project retrieval
function testProjectRetrieval($projectId) {
    global $db;
    
    try {
        echo "<div class='test-section'>";
        echo "<h3>🔍 Testing Project ID: {$projectId}</h3>";
        
        // First, check if project exists
        $project = $db->fetchOne(
            "SELECT * FROM projects WHERE id = ? AND is_active = 1",
            [$projectId]
        );
        
        if (!$project) {
            echo "<div class='warning'>";
            echo "<p>⚠️ Project ID {$projectId} not found or inactive</p>";
            echo "</div>";
            echo "</div>";
            return false;
        }
        
        echo "<div class='project-info'>";
        echo "<h4>📋 Project Details:</h4>";
        echo "<p><strong>Title:</strong> " . htmlspecialchars($project['title']) . "</p>";
        echo "<p><strong>Type:</strong> " . htmlspecialchars($project['project_type']) . "</p>";
        echo "<p><strong>Location:</strong> " . htmlspecialchars($project['location'] ?? 'N/A') . "</p>";
        echo "<p><strong>Gallery:</strong> " . htmlspecialchars($project['gallery'] ?? 'null') . "</p>";
        echo "</div>";
        
        // Test gallery JSON parsing
        $gallery = $project['gallery'] ?? null;
        $media = [];
        
        if ($gallery && $gallery !== 'null' && $gallery !== '') {
            echo "<h4>🖼️ Testing Gallery JSON:</h4>";
            
            try {
                $galleryIds = json_decode($gallery, true);
                
                if (json_last_error() !== JSON_ERROR_NONE) {
                    echo "<div class='error'>";
                    echo "<p>❌ JSON decode error: " . json_last_error_msg() . "</p>";
                    echo "<p>Gallery data: " . htmlspecialchars($gallery) . "</p>";
                    echo "</div>";
                } else {
                    echo "<div class='success'>";
                    echo "<p>✅ Gallery JSON parsed successfully</p>";
                    echo "<p>Gallery IDs: " . implode(', ', $galleryIds ?: []) . "</p>";
                    echo "</div>";
                    
                    // Test media retrieval
                    if (is_array($galleryIds) && !empty($galleryIds)) {
                        $placeholders = str_repeat('?,', count($galleryIds) - 1) . '?';
                        $media = $db->fetchAll(
                            "SELECT id, filename, original_name FROM media WHERE id IN ($placeholders) AND is_active = 1 ORDER BY id",
                            $galleryIds
                        );
                        
                        echo "<h4>🎨 Media Files Found:</h4>";
                        if (!empty($media)) {
                            echo "<ul>";
                            foreach ($media as $mediaItem) {
                                echo "<li>ID: {$mediaItem['id']} - {$mediaItem['original_name']} ({$mediaItem['filename']})</li>";
                            }
                            echo "</ul>";
                        } else {
                            echo "<p>No media files found for gallery IDs</p>";
                        }
                    }
                }
            } catch (Exception $e) {
                echo "<div class='error'>";
                echo "<p>❌ Gallery processing error: " . $e->getMessage() . "</p>";
                echo "</div>";
            }
        } else {
            echo "<p>📷 No gallery data</p>";
        }
        
        // Test the API response format
        echo "<h4>📡 API Response Format:</h4>";
        $apiResponse = [
            'success' => true,
            'data' => [
                'project' => $project,
                'media' => $media
            ]
        ];
        
        echo "<pre>" . json_encode($apiResponse, JSON_PRETTY_PRINT) . "</pre>";
        
        echo "<div class='success'>";
        echo "<p>✅ Project {$projectId} processed successfully</p>";
        echo "</div>";
        
        echo "</div>";
        return true;
        
    } catch (Exception $e) {
        echo "<div class='error'>";
        echo "<p>❌ Error testing project {$projectId}: " . $e->getMessage() . "</p>";
        echo "<p>Stack trace:</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
        echo "</div>";
        echo "</div>";
        return false;
    }
}

// Run tests
echo "<div class='test-section'>";
echo "<h2>🔌 Database Connection Test</h2>";
$dbTest = testDatabaseConnection();
if ($dbTest['success']) {
    echo "<div class='success'>";
    echo "<p>✅ Database connection successful</p>";
    echo "<p>Total projects in database: {$dbTest['count']}</p>";
    echo "</div>";
} else {
    echo "<div class='error'>";
    echo "<p>❌ Database connection failed: {$dbTest['error']}</p>";
    echo "</div>";
}
echo "</div>";

// Test each project ID
foreach ($testProjectIds as $projectId) {
    testProjectRetrieval($projectId);
}

// Test the actual API endpoint
echo "<div class='test-section'>";
echo "<h2>🌐 API Endpoint Test</h2>";
echo "<p>To test the actual API endpoint, you can use:</p>";
echo "<ul>";
foreach ($testProjectIds as $projectId) {
    $url = "http://localhost/erdevwe/api/mobile.php?action=project&id={$projectId}";
    echo "<li><a href='{$url}' target='_blank'>Test Project {$projectId}</a></li>";
}
echo "</ul>";
echo "<p><strong>Note:</strong> API endpoints require authentication. Use the debug tool or login first.</p>";
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>🛠️ Debugging Tools</h2>";
echo "<ul>";
echo "<li><a href='mobile-app/debug-auth.html' target='_blank'>Authentication Debug Tool</a></li>";
echo "<li><a href='mobile-app/' target='_blank'>Mobile App</a></li>";
echo "</ul>";
echo "</div>";
?>
